import 'dart:typed_data';
import 'package:flutter/foundation.dart';
// import 'package:image_picker/image_picker.dart';  // Commented out for web compatibility
import 'package:file_picker/file_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';

/// Professional service for handling profile image uploads to Supabase
/// Uses the existing 'profile-images' bucket with path: profile-images/users/{user_id}.jpg
class ProfileImageService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static const String _bucketName = 'profile-images'; // Existing public bucket
  static const String _folderPath = 'users'; // Folder within bucket

  /// Upload profile image and return the public URL
  ///
  /// [imageFile] - The selected image file
  /// [userId] - The user's ID for the file path
  ///
  /// Returns the public URL of the uploaded image or null if failed
  static Future<String?> uploadProfileImage({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    try {
      // Validate authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('❌ Upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own profile image
      if (currentUser.id != userId) {
        if (kDebugMode) {
          print('❌ Upload failed: User ID mismatch');
        }
        return null;
      }

      // Create file path: profile-images/users/{user_id}.jpg
      final fileName = '$userId.jpg';
      final filePath = '$_folderPath/$fileName';

      if (kDebugMode) {
        print('📤 Uploading profile image...');
        print('   Bucket: $_bucketName');
        print('   Path: $filePath');
      }

      // Read image bytes
      final Uint8List? imageBytes = imageFile.bytes;
      if (imageBytes == null) {
        if (kDebugMode) {
          print('❌ No image data available');
        }
        return null;
      }

      // Validate file size (max 5MB)
      const maxSizeBytes = 5 * 1024 * 1024; // 5MB
      if (imageBytes.length > maxSizeBytes) {
        if (kDebugMode) {
          print('❌ Upload failed: File too large (${imageBytes.length} bytes)');
        }
        return null;
      }

      // Upload options for optimal performance
      const uploadOptions = FileOptions(
        cacheControl: '3600', // Cache for 1 hour
        upsert: true, // Overwrite existing file
        contentType: 'image/jpeg',
      );

      // Upload to Supabase Storage
      final response = await _supabase.storage
          .from(_bucketName)
          .uploadBinary(filePath, imageBytes, fileOptions: uploadOptions);

      if (response.isNotEmpty) {
        // Generate public URL
        final publicUrl =
            _supabase.storage.from(_bucketName).getPublicUrl(filePath);

        if (kDebugMode) {
          print('✅ Profile image uploaded successfully');
          print('   Public URL: $publicUrl');
        }

        return publicUrl;
      } else {
        if (kDebugMode) {
          print('❌ Upload failed: Empty response from Supabase');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Profile image upload error: $e');
      }
      return null;
    }
  }

  /// Update user's profile image URL in the database
  ///
  /// [userId] - The user's ID
  /// [imageUrl] - The new profile image URL
  ///
  /// Returns true if successful, false otherwise
  static Future<bool> updateProfileImageUrl({
    required String userId,
    required String imageUrl,
  }) async {
    try {
      if (kDebugMode) {
        print('💾 Updating profile image URL in database...');
        print('   User ID: $userId');
        print('   Image URL: $imageUrl');
      }

      await _supabase.from('users').update({
        'profile_image_url': imageUrl,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', userId);

      if (kDebugMode) {
        print('✅ Profile image URL updated in database');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to update profile image URL: $e');
      }
      return false;
    }
  }

  /// Complete profile image upload process
  /// Uploads image and updates database in one operation
  ///
  /// [imageFile] - The selected image file
  /// [userId] - The user's ID
  ///
  /// Returns the public URL if successful, null otherwise
  static Future<String?> uploadAndUpdateProfileImage({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    try {
      // Step 1: Upload image to storage
      final imageUrl = await uploadProfileImage(
        imageFile: imageFile,
        userId: userId,
      );

      if (imageUrl == null) {
        return null;
      }

      // Step 2: Update database
      final success = await updateProfileImageUrl(
        userId: userId,
        imageUrl: imageUrl,
      );

      if (!success) {
        if (kDebugMode) {
          print('⚠️ Image uploaded but database update failed');
        }
        // Still return the URL since the image was uploaded successfully
      }

      return imageUrl;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Complete upload process failed: $e');
      }
      return null;
    }
  }

  /// Get the expected public URL for a user's profile image
  ///
  /// [userId] - The user's ID
  ///
  /// Returns the expected public URL
  static String getProfileImageUrl(String userId) {
    final filePath = '$_folderPath/$userId.jpg';
    return _supabase.storage.from(_bucketName).getPublicUrl(filePath);
  }

  /// Delete a user's profile image
  ///
  /// [userId] - The user's ID
  ///
  /// Returns true if successful, false otherwise
  static Future<bool> deleteProfileImage(String userId) async {
    try {
      final filePath = '$_folderPath/$userId.jpg';

      if (kDebugMode) {
        print('🗑️ Deleting profile image: $filePath');
      }

      final response =
          await _supabase.storage.from(_bucketName).remove([filePath]);

      if (response.isNotEmpty) {
        if (kDebugMode) {
          print('✅ Profile image deleted successfully');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ Failed to delete profile image');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting profile image: $e');
      }
      return false;
    }
  }

  /// Check if a profile image exists for a user
  ///
  /// [userId] - The user's ID
  ///
  /// Returns true if image exists, false otherwise
  static Future<bool> profileImageExists(String userId) async {
    try {
      final response = await _supabase.storage.from(_bucketName).list(
          path: _folderPath,
          searchOptions: SearchOptions(
            search: '$userId.jpg',
          ));

      return response.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking profile image existence: $e');
      }
      return false;
    }
  }
}
