class ConversationModel {
  final String id;
  final String driverId;
  final String passengerId;
  final String? tripId;
  final String? bookingId;
  final String? lastMessage;
  final DateTime? lastMessageAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int unreadCountDriver;
  final int unreadCountPassenger;

  // Related data (populated via joins)
  final String? driverName;
  final String? driverImage;
  final String? passengerName;
  final String? passengerImage;
  final String? tripFrom;
  final String? tripTo;

  const ConversationModel({
    required this.id,
    required this.driverId,
    required this.passengerId,
    this.tripId,
    this.bookingId,
    this.lastMessage,
    this.lastMessageAt,
    required this.createdAt,
    required this.updatedAt,
    this.unreadCountDriver = 0,
    this.unreadCountPassenger = 0,
    this.driverName,
    this.driverImage,
    this.passengerName,
    this.passengerImage,
    this.tripFrom,
    this.tripTo,
  });

  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] as String,
      driverId: json['driver_id'] as String,
      passengerId: json['passenger_id'] as String,
      tripId: json['trip_id'] as String?,
      bookingId: json['booking_id'] as String?,
      lastMessage: json['last_message'] as String?,
      lastMessageAt: json['last_message_at'] != null
          ? DateTime.parse(json['last_message_at'] as String)
          : null,
      unreadCountDriver: json['unread_count_driver'] as int? ?? 0,
      unreadCountPassenger: json['unread_count_passenger'] as int? ?? 0,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
      // Related data - handle both flat and nested formats
      driverName: json['driver_name'] as String? ?? json['driver']?['full_name'] as String?,
      driverImage: json['driver_image'] as String? ?? json['driver']?['profile_image_url'] as String?,
      passengerName: json['passenger_name'] as String? ?? json['passenger']?['full_name'] as String?,
      passengerImage: json['passenger_image'] as String? ?? json['passenger']?['profile_image_url'] as String?,
      tripFrom: json['trip_from'] as String? ?? json['trip']?['from_city'] as String? ?? json['trip']?['route_from'] as String?,
      tripTo: json['trip_to'] as String? ?? json['trip']?['to_city'] as String? ?? json['trip']?['route_to'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'driver_id': driverId,
      'passenger_id': passengerId,
      'trip_id': tripId,
      'booking_id': bookingId,
      'last_message': lastMessage,
      'last_message_at': lastMessageAt?.toIso8601String(),
      'unread_count_driver': unreadCountDriver,
      'unread_count_passenger': unreadCountPassenger,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'driver_name': driverName,
      'driver_image': driverImage,
      'passenger_name': passengerName,
      'passenger_image': passengerImage,
      'trip_from': tripFrom,
      'trip_to': tripTo,
    };
  }

  // Helper methods
  String getOtherUserName(String currentUserId) {
    if (currentUserId == driverId) {
      return passengerName ?? 'مسافر';
    } else {
      return driverName ?? 'سائق';
    }
  }

  String? getOtherUserImage(String currentUserId) {
    if (currentUserId == driverId) {
      return passengerImage;
    } else {
      return driverImage;
    }
  }

  String getOtherUserId(String currentUserId) {
    if (currentUserId == driverId) {
      return passengerId;
    } else {
      return driverId;
    }
  }

  int getUnreadCount(String currentUserId) {
    if (currentUserId == driverId) {
      return unreadCountDriver;
    } else {
      return unreadCountPassenger;
    }
  }

  String get tripRoute {
    if (tripFrom != null && tripTo != null) {
      return '$tripFrom ← $tripTo';
    }
    return 'رحلة';
  }

  String get formattedLastMessageTime {
    if (lastMessageAt == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(lastMessageAt!);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} س';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ي';
    } else {
      return '${lastMessageAt!.day}/${lastMessageAt!.month}';
    }
  }

  ConversationModel copyWith({
    String? id,
    String? driverId,
    String? passengerId,
    String? tripId,
    String? bookingId,
    String? lastMessage,
    DateTime? lastMessageAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? unreadCountDriver,
    int? unreadCountPassenger,
    String? driverName,
    String? driverImage,
    String? passengerName,
    String? passengerImage,
    String? tripFrom,
    String? tripTo,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      driverId: driverId ?? this.driverId,
      passengerId: passengerId ?? this.passengerId,
      tripId: tripId ?? this.tripId,
      bookingId: bookingId ?? this.bookingId,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      unreadCountDriver: unreadCountDriver ?? this.unreadCountDriver,
      unreadCountPassenger: unreadCountPassenger ?? this.unreadCountPassenger,
      driverName: driverName ?? this.driverName,
      driverImage: driverImage ?? this.driverImage,
      passengerName: passengerName ?? this.passengerName,
      passengerImage: passengerImage ?? this.passengerImage,
      tripFrom: tripFrom ?? this.tripFrom,
      tripTo: tripTo ?? this.tripTo,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConversationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ConversationModel(id: $id, driverId: $driverId, passengerId: $passengerId, tripRoute: $tripRoute)';
  }
}
