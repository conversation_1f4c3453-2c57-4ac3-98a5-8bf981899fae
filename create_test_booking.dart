import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Create test booking data to verify the driver dashboard works
Future<void> main() async {
  print('🧪 Creating test booking data for Safarni app...');

  try {
    // Initialize Supabase
    await Supabase.initialize(
      url: 'https://pmykpbrecfoxgtahetgb.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBteWtwYnJlY2ZveGd0YWhldGdiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNzQ3ODMsImV4cCI6MjA2NTc1MDc4M30.jxkxOW46KnVWUGR8TClVWMlOevRcwrUSfiZfQ6qMDgE',
    );

    final client = Supabase.instance.client;
    print('✅ Supabase client initialized');
    
    // First, let's check what users exist
    print('\n🔍 Checking existing users...');
    final users = await client
        .from('users')
        .select('id, full_name, role')
        .limit(5);
    
    if (users.isEmpty) {
      print('❌ No users found. Please create some users first.');
      return;
    }
    
    print('📊 Found ${users.length} users:');
    for (var user in users) {
      print('   - ${user['full_name']} (${user['role']}) - ID: ${user['id']}');
    }
    
    // Find a driver and a passenger
    final drivers = users.where((u) => u['role'] == 'trip_leader' || u['role'] == 'driver').toList();
    final passengers = users.where((u) => u['role'] == 'traveler' || u['role'] == 'passenger').toList();
    
    if (drivers.isEmpty) {
      print('❌ No drivers found. Creating a test driver...');
      // Create a test driver
      final newDriver = await client
          .from('users')
          .insert({
            'id': '11111111-1111-1111-1111-111111111111',
            'full_name': 'أحمد السائق',
            'role': 'trip_leader',
            'phone': '+212600000001',
            'city': 'الرباط',
          })
          .select()
          .single();
      drivers.add(newDriver);
      print('✅ Created test driver: ${newDriver['full_name']}');
    }
    
    if (passengers.isEmpty) {
      print('❌ No passengers found. Creating a test passenger...');
      // Create a test passenger
      final newPassenger = await client
          .from('users')
          .insert({
            'id': '22222222-2222-2222-2222-222222222222',
            'full_name': 'فاطمة المسافرة',
            'role': 'traveler',
            'phone': '+212600000002',
            'city': 'الدار البيضاء',
          })
          .select()
          .single();
      passengers.add(newPassenger);
      print('✅ Created test passenger: ${newPassenger['full_name']}');
    }
    
    final driver = drivers.first;
    final passenger = passengers.first;
    
    print('\n🚗 Using driver: ${driver['full_name']} (${driver['id']})');
    print('🎫 Using passenger: ${passenger['full_name']} (${passenger['id']})');
    
    // Check if there are any trips
    print('\n🔍 Checking existing trips...');
    final trips = await client
        .from('trips')
        .select('id, from_city, to_city, driver_id, status')
        .eq('driver_id', driver['id'])
        .limit(3);
    
    String tripId;
    if (trips.isEmpty) {
      print('❌ No trips found for driver. Creating a test trip...');
      // Create a test trip
      final newTrip = await client
          .from('trips')
          .insert({
            'driver_id': driver['id'],
            'leader_id': driver['id'],
            'from_city': 'الرباط',
            'to_city': 'الدار البيضاء',
            'departure_time': DateTime.now().add(Duration(hours: 2)).toIso8601String(),
            'available_seats': 3,
            'price_per_seat': 50.0,
            'status': 'published',
            'trip_type': 'one_way',
            'car_type': 'sedan',
            'car_color': 'أبيض',
            'car_plate': 'أ-12345-ب',
            'is_instant_booking': true,
            'rules': 'ممنوع التدخين',
            'notes': 'رحلة مريحة وآمنة',
          })
          .select()
          .single();
      tripId = newTrip['id'];
      print('✅ Created test trip: ${newTrip['from_city']} → ${newTrip['to_city']}');
    } else {
      tripId = trips.first['id'];
      print('✅ Using existing trip: ${trips.first['from_city']} → ${trips.first['to_city']}');
    }
    
    // Create test bookings
    print('\n📋 Creating test bookings...');
    
    // Create a pending booking
    try {
      final pendingBooking = await client
          .from('bookings')
          .insert({
            'trip_id': tripId,
            'passenger_id': passenger['id'],
            'driver_id': driver['id'],
            'seats_booked': 1,
            'total_price': 50.0,
            'status': 'pending',
            'booking_type': 'manual',
            'message': 'مرحبا، أريد حجز مقعد في هذه الرحلة',
            'passenger_details': {
              'name': passenger['full_name'],
              'phone': passenger['phone'] ?? '+212600000002',
              'has_luggage': true,
            },
          })
          .select()
          .single();
      print('✅ Created pending booking: ${pendingBooking['id']}');
    } catch (e) {
      print('⚠️ Pending booking creation failed (may already exist): $e');
    }
    
    // Create an accepted booking
    try {
      final acceptedBooking = await client
          .from('bookings')
          .insert({
            'trip_id': tripId,
            'passenger_id': passenger['id'],
            'driver_id': driver['id'],
            'seats_booked': 2,
            'total_price': 100.0,
            'status': 'accepted',
            'booking_type': 'instant',
            'message': 'حجز فوري',
            'confirmed_at': DateTime.now().toIso8601String(),
            'passenger_details': {
              'name': passenger['full_name'],
              'phone': passenger['phone'] ?? '+212600000002',
              'has_luggage': false,
            },
          })
          .select()
          .single();
      print('✅ Created accepted booking: ${acceptedBooking['id']}');
    } catch (e) {
      print('⚠️ Accepted booking creation failed (may already exist): $e');
    }
    
    // Test the database functions
    print('\n🧪 Testing database functions...');
    
    try {
      final driverBookings = await client.rpc('get_driver_bookings', params: {
        'p_driver_id': driver['id'],
        'p_status': null,
      });
      print('✅ get_driver_bookings function works: ${driverBookings.length} bookings');
    } catch (e) {
      print('❌ get_driver_bookings function failed: $e');
    }
    
    try {
      final pendingBookings = await client.rpc('get_driver_bookings', params: {
        'p_driver_id': driver['id'],
        'p_status': 'pending',
      });
      print('✅ get_driver_bookings (pending) function works: ${pendingBookings.length} bookings');
    } catch (e) {
      print('❌ get_driver_bookings (pending) function failed: $e');
    }
    
    print('\n🎉 Test data creation completed!');
    print('📝 Summary:');
    print('   - Driver ID: ${driver['id']}');
    print('   - Passenger ID: ${passenger['id']}');
    print('   - Trip ID: $tripId');
    print('   - Test bookings created with pending and accepted statuses');
    print('\n💡 Now you can test the driver dashboard with real data!');
    
  } catch (e) {
    print('❌ Critical error during test data creation: $e');
    exit(1);
  }
}
