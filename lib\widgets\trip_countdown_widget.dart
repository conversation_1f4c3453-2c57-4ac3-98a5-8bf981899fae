import 'dart:async';
import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../models/trip_model.dart';
import '../services/trip_reminder_service.dart';

class TripCountdownWidget extends StatefulWidget {
  final TripModel trip;
  final bool showActions;
  final VoidCallback? onComplete;
  final VoidCallback? onConfirmArrival;

  const TripCountdownWidget({
    super.key,
    required this.trip,
    this.showActions = false,
    this.onComplete,
    this.onConfirmArrival,
  });

  @override
  State<TripCountdownWidget> createState() => _TripCountdownWidgetState();
}

class _TripCountdownWidgetState extends State<TripCountdownWidget> {
  Timer? _timer;
  Map<String, dynamic> _countdownInfo = {};

  @override
  void initState() {
    super.initState();
    _updateCountdown();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateCountdown();
    });
  }

  void _updateCountdown() {
    final countdownInfo = TripReminderService.getTripCountdown(widget.trip);
    if (mounted) {
      setState(() {
        _countdownInfo = countdownInfo;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_countdownInfo.isEmpty) {
      return const SizedBox.shrink();
    }

    final status = _countdownInfo['status'] as String;
    final message = _countdownInfo['message'] as String;
    final isUrgent = _countdownInfo['urgent'] as bool? ?? false;
    final timeLeft = _countdownInfo['timeLeft'] as Duration;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isUrgent
              ? [AppColors.error.withValues(alpha: 0.1), AppColors.warning.withValues(alpha: 0.1)]
              : [AppColors.primary.withValues(alpha: 0.1), AppColors.secondary.withValues(alpha: 0.1)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isUrgent ? AppColors.error.withValues(alpha: 0.3) : AppColors.primary.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trip info header
          Row(
            children: [
              Icon(
                Icons.schedule,
                color: isUrgent ? AppColors.error : AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${widget.trip.fromCity} ← ${widget.trip.toCity}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: isUrgent ? AppColors.error : AppColors.primary,
                      ),
                    ),
                    Text(
                      '${widget.trip.departureDate.day}/${widget.trip.departureDate.month} - ${widget.trip.departureTime}',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              if (isUrgent)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.error,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'عاجل',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Countdown display
          _buildCountdownDisplay(status, message, timeLeft, isUrgent),

          // Action buttons
          if (widget.showActions && status != 'past') ...[
            const SizedBox(height: 16),
            _buildActionButtons(status, isUrgent),
          ],
        ],
      ),
    );
  }

  Widget _buildCountdownDisplay(String status, String message, Duration timeLeft, bool isUrgent) {
    switch (status) {
      case 'days':
      case 'hours':
      case 'minutes':
        return _buildDetailedCountdown(timeLeft, isUrgent);
      case 'now':
        return _buildNowDisplay();
      case 'past':
        return _buildPastDisplay();
      default:
        return Text(
          message,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isUrgent ? AppColors.error : AppColors.primary,
          ),
        );
    }
  }

  Widget _buildDetailedCountdown(Duration timeLeft, bool isUrgent) {
    final days = timeLeft.inDays;
    final hours = timeLeft.inHours % 24;
    final minutes = timeLeft.inMinutes % 60;
    final seconds = timeLeft.inSeconds % 60;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        if (days > 0) _buildTimeUnit('يوم', days, isUrgent),
        if (hours > 0 || days > 0) _buildTimeUnit('ساعة', hours, isUrgent),
        if (minutes > 0 || hours > 0 || days > 0) _buildTimeUnit('دقيقة', minutes, isUrgent),
        if (days == 0 && hours == 0) _buildTimeUnit('ثانية', seconds, isUrgent),
      ],
    );
  }

  Widget _buildTimeUnit(String label, int value, bool isUrgent) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isUrgent ? AppColors.error : AppColors.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            value.toString().padLeft(2, '0'),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: isUrgent ? AppColors.error : AppColors.primary,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildNowDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.success,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.directions_car,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Text(
            'حان وقت الانطلاق!',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPastDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textTertiary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.white,
            size: 24,
          ),
          SizedBox(width: 12),
          Text(
            'انتهت الرحلة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(String status, bool isUrgent) {
    return Row(
      children: [
        if (status == 'now' || isUrgent) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: widget.onConfirmArrival,
              icon: const Icon(Icons.location_on, size: 18),
              label: const Text('تأكيد الوصول'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
        ],
        Expanded(
          child: OutlinedButton.icon(
            onPressed: widget.onComplete,
            icon: const Icon(Icons.done, size: 18),
            label: const Text('إكمال الرحلة'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: BorderSide(color: AppColors.primary),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }
}
