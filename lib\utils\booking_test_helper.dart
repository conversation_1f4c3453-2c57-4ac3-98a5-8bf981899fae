import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class BookingTestHelper {
  static final _client = Supabase.instance.client;

  /// Create test bookings using direct SQL inserts (more reliable)
  static Future<void> createTestBookingsSQL() async {
    try {
      if (kDebugMode) {
        print('🧪 Creating test bookings using SQL...');
      }

      // Get current user (driver)
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('❌ No authenticated user');
        }
        return;
      }

      final driverId = currentUser.id;
      if (kDebugMode) {
        print('👤 Driver ID: $driverId');
      }

      // Create test passenger first
      await _client.from('users').upsert({
        'id': 'test-passenger-001',
        'email': '<EMAIL>',
        'full_name': 'فاطمة الزهراء',
        'phone': '+212612345678',
        'role': 'passenger',
        'city': 'الرباط',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('👥 Test passenger created/updated');
      }

      // Create test bookings for the driver's trips
      final testBookings = [
        {
          'id': 'test-booking-001',
          'trip_id': '1db7f4db-b209-4365-bda6-c2b9e3a3fddc', // From logs
          'passenger_id': 'test-passenger-001',
          'driver_id': driverId,
          'seats_booked': 1,
          'total_price': 220.0,
          'booking_type': 'manual',
          'status': 'pending',
          'message': 'أتطلع للرحلة معكم، أرجو قبول طلب الحجز',
          'special_requests': 'مقعد بجانب النافذة إذا أمكن',
          'passenger_details': {
            'passengers': [
              {
                'name': 'فاطمة الزهراء',
                'phone': '+212612345678',
                'age': 28,
              }
            ]
          },
          'is_paid': false,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        {
          'id': 'test-booking-002',
          'trip_id': '9456c729-c0c2-4897-93c8-cc86d0146c91', // From logs
          'passenger_id': 'test-passenger-001',
          'driver_id': driverId,
          'seats_booked': 2,
          'total_price': 150.0,
          'booking_type': 'manual',
          'status': 'pending',
          'message': 'هل يمكنني حجز مقعدين للرحلة؟',
          'special_requests': 'نحتاج مساحة إضافية للأمتعة',
          'passenger_details': {
            'passengers': [
              {
                'name': 'فاطمة الزهراء',
                'phone': '+212612345678',
                'age': 28,
              },
              {
                'name': 'أحمد محمد',
                'phone': '+212687654321',
                'age': 35,
              }
            ]
          },
          'is_paid': false,
          'created_at': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
          'updated_at': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
        }
      ];

      // Insert test bookings
      for (final booking in testBookings) {
        await _client.from('bookings').upsert(booking);
        if (kDebugMode) {
          print('✅ Test booking created: ${booking['id']}');
        }
      }

      if (kDebugMode) {
        print('✅ All test bookings created successfully');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating test bookings: $e');
      }
    }
  }

  /// Create a test booking directly in the database
  static Future<void> createTestBooking() async {
    try {
      if (kDebugMode) {
        print('🧪 Creating test booking...');
      }

      // Get current user (driver)
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('❌ No authenticated user');
        }
        return;
      }

      final driverId = currentUser.id;
      if (kDebugMode) {
        print('👤 Driver ID: $driverId');
      }

      // Get driver's first trip
      final tripsResponse = await _client
          .from('trips')
          .select('id, title, from_city, to_city, price')
          .eq('leader_id', driverId)
          .limit(1);

      if (tripsResponse.isEmpty) {
        if (kDebugMode) {
          print('❌ No trips found for driver');
        }
        return;
      }

      final trip = tripsResponse.first;
      final tripId = trip['id'] as String;
      if (kDebugMode) {
        print('🚗 Using trip: ${trip['title']} (${trip['id']})');
      }

      // Create test passenger
      final testPassengerId = 'test-passenger-${DateTime.now().millisecondsSinceEpoch}';
      
      await _client.from('users').upsert({
        'id': testPassengerId,
        'email': '<EMAIL>',
        'full_name': 'فاطمة الزهراء',
        'phone': '+212612345678',
        'role': 'passenger',
        'city': 'الرباط',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('👥 Test passenger created: $testPassengerId');
      }

      // Create test booking
      final testBookingId = 'test-booking-${DateTime.now().millisecondsSinceEpoch}';
      
      final bookingData = {
        'id': testBookingId,
        'trip_id': tripId,
        'passenger_id': testPassengerId,
        'driver_id': driverId,
        'seats_booked': 1,
        'total_price': (trip['price'] as num).toDouble(),
        'booking_type': 'manual',
        'status': 'pending',
        'message': 'أتطلع للرحلة معكم، أرجو قبول طلب الحجز',
        'special_requests': 'مقعد بجانب النافذة إذا أمكن',
        'passenger_details': {
          'passengers': [
            {
              'name': 'فاطمة الزهراء',
              'phone': '+212612345678',
              'age': 28,
            }
          ]
        },
        'is_paid': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _client.from('bookings').insert(bookingData);

      if (kDebugMode) {
        print('✅ Test booking created successfully: $testBookingId');
        print('   Trip: ${trip['from_city']} → ${trip['to_city']}');
        print('   Passenger: فاطمة الزهراء');
        print('   Status: pending');
        print('   Price: ${trip['price']} درهم');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating test booking: $e');
      }
    }
  }

  /// Check existing bookings for a driver
  static Future<void> checkDriverBookings(String driverId) async {
    try {
      if (kDebugMode) {
        print('🔍 Checking bookings for driver: $driverId');
      }

      final response = await _client
          .from('bookings')
          .select('''
            id,
            status,
            seats_booked,
            total_price,
            created_at,
            trip:trips(title, from_city, to_city),
            passenger:users!passenger_id(full_name)
          ''')
          .eq('driver_id', driverId)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('📊 Found ${response.length} bookings:');
        
        for (final booking in response) {
          print('   • ${booking['id']}');
          print('     Status: ${booking['status']}');
          print('     Passenger: ${booking['passenger']?['full_name'] ?? 'Unknown'}');
          print('     Trip: ${booking['trip']?['from_city']} → ${booking['trip']?['to_city']}');
          print('     Price: ${booking['total_price']} درهم');
          print('     Created: ${booking['created_at']}');
          print('');
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking bookings: $e');
      }
    }
  }
}
