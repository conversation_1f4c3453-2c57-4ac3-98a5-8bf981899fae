import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final Widget? action;
  final double iconSize;
  final Color? iconColor;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.action,
    this.iconSize = 80.0,
    this.iconColor,
    this.titleStyle,
    this.subtitleStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? AppColors.textTertiary,
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: titleStyle ??
                  TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: subtitleStyle ??
                    TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                      height: 1.4,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }

  // Factory constructors for common empty states
  factory EmptyStateWidget.conversations({
    VoidCallback? onCreateConversation,
  }) {
    return EmptyStateWidget(
      icon: Icons.chat_bubble_outline,
      title: 'لا توجد محادثات بعد',
      subtitle: 'ستظهر محادثاتك هنا عندما تبدأ في التواصل مع السائقين أو المسافرين',
      iconColor: AppColors.primary.withValues(alpha: 0.6),
      action: onCreateConversation != null
          ? ElevatedButton.icon(
              onPressed: onCreateConversation,
              icon: const Icon(Icons.add_comment),
              label: const Text('بدء محادثة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            )
          : null,
    );
  }

  factory EmptyStateWidget.bookings({
    VoidCallback? onSearchTrips,
  }) {
    return EmptyStateWidget(
      icon: Icons.bookmark_border,
      title: 'لا توجد حجوزات',
      subtitle: 'ابحث عن رحلات واحجز مقعدك للسفر مع سائقين موثوقين',
      iconColor: AppColors.secondary.withValues(alpha: 0.6),
      action: onSearchTrips != null
          ? ElevatedButton.icon(
              onPressed: onSearchTrips,
              icon: const Icon(Icons.search),
              label: const Text('البحث عن رحلات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            )
          : null,
    );
  }

  factory EmptyStateWidget.trips({
    VoidCallback? onCreateTrip,
  }) {
    return EmptyStateWidget(
      icon: Icons.directions_car_outlined,
      title: 'لا توجد رحلات',
      subtitle: 'أنشئ رحلتك الأولى وابدأ في استقبال طلبات الحجز من المسافرين',
      iconColor: AppColors.success.withValues(alpha: 0.6),
      action: onCreateTrip != null
          ? ElevatedButton.icon(
              onPressed: onCreateTrip,
              icon: const Icon(Icons.add_road),
              label: const Text('إنشاء رحلة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            )
          : null,
    );
  }

  factory EmptyStateWidget.messages({
    String? userName,
  }) {
    return EmptyStateWidget(
      icon: Icons.message_outlined,
      title: 'لا توجد رسائل بعد',
      subtitle: userName != null
          ? 'ابدأ محادثتك مع $userName'
          : 'ابدأ محادثتك الأولى',
      iconColor: AppColors.primary.withValues(alpha: 0.6),
    );
  }

  factory EmptyStateWidget.search({
    String? searchQuery,
    VoidCallback? onClearSearch,
  }) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: searchQuery != null
          ? 'لا توجد نتائج لـ "$searchQuery"'
          : 'لا توجد نتائج',
      subtitle: 'جرب البحث بكلمات مختلفة أو تحقق من الإملاء',
      iconColor: AppColors.warning.withValues(alpha: 0.6),
      action: onClearSearch != null
          ? TextButton.icon(
              onPressed: onClearSearch,
              icon: const Icon(Icons.clear),
              label: const Text('مسح البحث'),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            )
          : null,
    );
  }

  factory EmptyStateWidget.error({
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    return EmptyStateWidget(
      icon: Icons.error_outline,
      title: 'حدث خطأ',
      subtitle: errorMessage ?? 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى',
      iconColor: AppColors.error.withValues(alpha: 0.6),
      action: onRetry != null
          ? ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            )
          : null,
    );
  }

  factory EmptyStateWidget.noInternet({
    VoidCallback? onRetry,
  }) {
    return EmptyStateWidget(
      icon: Icons.wifi_off,
      title: 'لا يوجد اتصال بالإنترنت',
      subtitle: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      iconColor: AppColors.warning.withValues(alpha: 0.6),
      action: onRetry != null
          ? ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            )
          : null,
    );
  }

  factory EmptyStateWidget.maintenance() {
    return EmptyStateWidget(
      icon: Icons.build_outlined,
      title: 'الخدمة قيد الصيانة',
      subtitle: 'نعمل على تحسين الخدمة، يرجى المحاولة لاحقاً',
      iconColor: AppColors.info.withValues(alpha: 0.6),
    );
  }
}
