import 'dart:async';
import 'package:flutter/material.dart';
import 'lib/services/notification_service.dart';
import 'lib/services/booking_service.dart';
import 'lib/services/message_service.dart';
import 'lib/providers/notification_provider.dart';
import 'lib/models/notification_model.dart';

void main() async {
  print('🧪 Final Compilation Test - Testing all fixed methods...');
  
  try {
    // Test 1: NotificationService.getUserNotifications with limit
    print('\n1️⃣ Testing getUserNotifications with limit...');
    final notifications = await NotificationService.getUserNotifications('test-user', limit: 10);
    print('✅ getUserNotifications with limit works: ${notifications.length} notifications');
    
    // Test 2: subscribeToUserNotifications with callback
    print('\n2️⃣ Testing subscribeToUserNotifications with callback...');
    final subscription = NotificationService.subscribeToUserNotifications('test-user', (notification) {
      print('Callback received: ${notification.title}');
    });
    print('✅ subscribeToUserNotifications with callback works');
    
    // Test 3: Missing notification methods
    print('\n3️⃣ Testing missing notification methods...');
    
    await NotificationService.showLocalNotification(
      id: 1,
      title: 'Test Local',
      body: 'Test body',
    );
    print('✅ showLocalNotification works');
    
    await NotificationService.showBookingRequestNotification(
      driverName: 'Test Driver',
      passengerName: 'Test Passenger',
      tripRoute: 'Test Route',
      bookingId: 'test-booking',
    );
    print('✅ showBookingRequestNotification works');
    
    await NotificationService.sendBookingRequestNotification(
      driverId: 'driver-id',
      passengerId: 'passenger-id',
      bookingId: 'booking-id',
      tripRoute: 'Test Route',
      passengerName: 'Test Passenger',
    );
    print('✅ sendBookingRequestNotification works');
    
    await NotificationService.sendBookingAcceptanceNotification(
      passengerId: 'passenger-id',
      driverId: 'driver-id',
      bookingId: 'booking-id',
      tripRoute: 'Test Route',
      driverName: 'Test Driver',
    );
    print('✅ sendBookingAcceptanceNotification works');
    
    await NotificationService.sendBookingRejectionNotification(
      passengerId: 'passenger-id',
      driverId: 'driver-id',
      bookingId: 'booking-id',
      tripRoute: 'Test Route',
      driverName: 'Test Driver',
      rejectionReason: 'Test reason',
    );
    print('✅ sendBookingRejectionNotification works');
    
    // Test 4: sendNotification with correct parameters
    print('\n4️⃣ Testing sendNotification with correct parameters...');
    await NotificationService.sendNotification(
      userId: 'test-user',
      title: 'Test Title',
      message: 'Test message', // Using 'message' not 'body'
      type: 'test',
    );
    print('✅ sendNotification with message parameter works');
    
    // Test 5: createNotification with correct parameters
    print('\n5️⃣ Testing createNotification with correct parameters...');
    await NotificationService.createNotification(
      userId: 'test-user',
      title: 'Test Title',
      message: 'Test message', // Using 'message' not 'body'
      type: 'test',
    );
    print('✅ createNotification with message parameter works');
    
    // Test 6: getUnreadCount without FetchOptions
    print('\n6️⃣ Testing getUnreadCount without FetchOptions...');
    final unreadCount = await NotificationService.getUnreadCount('test-user');
    print('✅ getUnreadCount works: $unreadCount unread notifications');
    
    print('\n🎉 ALL COMPILATION TESTS PASSED! 🎉');
    print('✅ All method signatures are correct');
    print('✅ All missing methods are implemented');
    print('✅ All type mismatches are fixed');
    print('✅ All parameter names are correct');
    print('✅ FetchOptions usage is fixed');
    print('\n🚀 Ready to run: flutter run -d chrome');
    
  } catch (e, stackTrace) {
    print('❌ Compilation test failed: $e');
    print('Stack trace: $stackTrace');
  }
}
