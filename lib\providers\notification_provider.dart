import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';

class NotificationProvider with ChangeNotifier {
  final List<NotificationModel> _notifications = [];
  int _unreadCount = 0;
  bool _isLoading = false;
  
  RealtimeChannel? _notificationChannel;
  String? _currentUserId;

  List<NotificationModel> get notifications => List.unmodifiable(_notifications);
  int get unreadCount => _unreadCount;
  bool get isLoading => _isLoading;

  // Initialize notifications for a user
  Future<void> initialize(String userId) async {
    if (_currentUserId == userId) return; // Already initialized for this user
    
    _currentUserId = userId;
    await _loadNotifications();
    _setupRealtimeSubscription();
  }

  // Clean up when user logs out
  void dispose() {
    _cleanupSubscriptions();
    _notifications.clear();
    _unreadCount = 0;
    _currentUserId = null;
    super.dispose();
  }

  // Load notifications from server
  Future<void> _loadNotifications() async {
    if (_currentUserId == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      final notifications = await NotificationService.getUserNotifications(
        _currentUserId!,
        limit: 50,
      );
      
      final unreadCount = await NotificationService.getUnreadCount(_currentUserId!);

      _notifications.clear();
      _notifications.addAll(notifications);
      _unreadCount = unreadCount;
      
      if (kDebugMode) {
        print('📱 Loaded ${notifications.length} notifications, $unreadCount unread');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading notifications: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Setup real-time subscription
  void _setupRealtimeSubscription() {
    if (_currentUserId == null) return;

    _cleanupSubscriptions(); // Clean up any existing subscription

    _notificationChannel = NotificationService.subscribeToUserNotifications(
      _currentUserId!,
      (notification) {
        _addNotification(notification);
      },
    );

    if (kDebugMode) {
      print('🔔 Setup notification subscription for user: $_currentUserId');
    }
  }

  // Clean up subscriptions
  void _cleanupSubscriptions() {
    if (_notificationChannel != null) {
      NotificationService.unsubscribeFromNotifications(_notificationChannel!);
      _notificationChannel = null;
    }
  }

  // Add new notification (from real-time subscription)
  void _addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    if (!notification.isRead) {
      _unreadCount++;
    }
    notifyListeners();

    if (kDebugMode) {
      print('🔔 New notification: ${notification.title}');
    }
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await NotificationService.markAsRead(notificationId);
      
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1 && !_notifications[index].isRead) {
        _notifications[index] = _notifications[index].copyWith(
          isRead: true,
          readAt: DateTime.now(),
        );
        _unreadCount = (_unreadCount - 1).clamp(0, _unreadCount);
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    if (_currentUserId == null) return;

    try {
      await NotificationService.markAllAsRead(_currentUserId!);
      
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
      }
      _unreadCount = 0;
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking all notifications as read: $e');
      }
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await NotificationService.deleteNotification(notificationId);
      
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final notification = _notifications[index];
        _notifications.removeAt(index);
        if (!notification.isRead) {
          _unreadCount = (_unreadCount - 1).clamp(0, _unreadCount);
        }
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting notification: $e');
      }
    }
  }

  // Refresh notifications
  Future<void> refresh() async {
    await _loadNotifications();
  }

  // Get notifications by type
  List<NotificationModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.notificationType == type).toList();
  }

  // Get unread notifications
  List<NotificationModel> get unreadNotifications {
    return _notifications.where((n) => !n.isRead).toList();
  }

  // Check if there are unread booking requests
  bool get hasUnreadBookingRequests {
    return _notifications.any((n) => 
        n.notificationType == 'booking_request' && !n.isRead);
  }

  // Check if there are unread messages
  bool get hasUnreadMessages {
    return _notifications.any((n) => 
        n.notificationType == 'message' && !n.isRead);
  }

  // Get unread count by type
  int getUnreadCountByType(String type) {
    return _notifications
        .where((n) => n.notificationType == type && !n.isRead)
        .length;
  }

  // Create and send notification (for testing or admin purposes)
  Future<void> createNotification({
    required String userId,
    required String title,
    required String body,
    String notificationType = 'general',
    String? tripId,
    String? bookingId,
    String? relatedUserId,
    Map<String, dynamic>? data,
  }) async {
    try {
      await NotificationService.sendNotification(
        userId: userId,
        title: title,
        message: body,
        notificationType: notificationType,
        tripId: tripId,
        bookingId: bookingId,
        relatedUserId: relatedUserId,
        data: data,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }
    }
  }

  // Handle notification tap (for navigation)
  void handleNotificationTap(NotificationModel notification) {
    // Mark as read if not already read
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    // Additional navigation logic can be added here
    // This method can be called from UI components to handle navigation
  }
}
