import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Test script to verify database functions are working
Future<void> main() async {
  print('🧪 Testing Safarni database functions...');
  
  try {
    // Initialize Supabase
    await Supabase.initialize(
      url: 'https://pmykpbrecfoxgtahetgb.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBteWtwYnJlY2ZveGd0YWhldGdiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNzQ3ODMsImV4cCI6MjA2NTc1MDc4M30.jxkxOW46KnVWUGR8TClVWMlOevRcwrUSfiZfQ6qMDgE',
    );
    
    final client = Supabase.instance.client;
    print('✅ Supabase client initialized');
    
    // Test 1: Check if get_or_create_conversation function exists
    print('\n🔍 Test 1: Testing get_or_create_conversation function...');
    try {
      final result = await client.rpc('get_or_create_conversation', params: {
        'p_trip_id': '00000000-0000-0000-0000-000000000000',
        'p_booking_id': null,
        'p_driver_id': '00000000-0000-0000-0000-000000000000',
        'p_passenger_id': '11111111-1111-1111-1111-111111111111',
      });
      print('✅ get_or_create_conversation function works: $result');
    } catch (e) {
      print('❌ get_or_create_conversation function failed: $e');
    }
    
    // Test 2: Check if get_passenger_bookings function exists
    print('\n🔍 Test 2: Testing get_passenger_bookings function...');
    try {
      final result = await client.rpc('get_passenger_bookings', params: {
        'p_passenger_id': '00000000-0000-0000-0000-000000000000',
        'p_status': null,
      });
      print('✅ get_passenger_bookings function works: ${result.length} results');
    } catch (e) {
      print('❌ get_passenger_bookings function failed: $e');
    }
    
    // Test 3: Check if get_driver_bookings function exists
    print('\n🔍 Test 3: Testing get_driver_bookings function...');
    try {
      final result = await client.rpc('get_driver_bookings', params: {
        'p_driver_id': '00000000-0000-0000-0000-000000000000',
        'p_status': null,
      });
      print('✅ get_driver_bookings function works: ${result.length} results');
    } catch (e) {
      print('❌ get_driver_bookings function failed: $e');
    }
    
    // Test 4: Check conversations table structure
    print('\n🔍 Test 4: Testing conversations table structure...');
    try {
      final result = await client
          .from('conversations')
          .select('id, booking_id, is_active, unread_count_driver, unread_count_passenger')
          .limit(1);
      print('✅ Conversations table structure is correct: ${result.length} rows');
    } catch (e) {
      print('❌ Conversations table test failed: $e');
    }
    
    // Test 5: Check notifications table
    print('\n🔍 Test 5: Testing notifications table...');
    try {
      final result = await client
          .from('notifications')
          .select('id, user_id, type, title, message, is_read')
          .limit(1);
      print('✅ Notifications table is accessible: ${result.length} rows');
    } catch (e) {
      print('❌ Notifications table test failed: $e');
    }
    
    // Test 6: Check bookings table
    print('\n🔍 Test 6: Testing bookings table...');
    try {
      final result = await client
          .from('bookings')
          .select('id, driver_id, passenger_id, status, created_at')
          .limit(5);
      print('✅ Bookings table accessible: ${result.length} bookings found');
      
      if (result.isNotEmpty) {
        print('📊 Sample booking data:');
        for (var booking in result) {
          print('   - ID: ${booking['id']}, Status: ${booking['status']}, Driver: ${booking['driver_id']}');
        }
      }
    } catch (e) {
      print('❌ Bookings table test failed: $e');
    }
    
    // Test 7: Check users table
    print('\n🔍 Test 7: Testing users table...');
    try {
      final result = await client
          .from('users')
          .select('id, full_name, role')
          .limit(3);
      print('✅ Users table accessible: ${result.length} users found');
      
      if (result.isNotEmpty) {
        print('📊 Sample user data:');
        for (var user in result) {
          print('   - ID: ${user['id']}, Name: ${user['full_name']}, Role: ${user['role']}');
        }
      }
    } catch (e) {
      print('❌ Users table test failed: $e');
    }
    
    // Test 8: Check trips table
    print('\n🔍 Test 8: Testing trips table...');
    try {
      final result = await client
          .from('trips')
          .select('id, from_city, to_city, status, driver_id')
          .limit(3);
      print('✅ Trips table accessible: ${result.length} trips found');
      
      if (result.isNotEmpty) {
        print('📊 Sample trip data:');
        for (var trip in result) {
          print('   - ID: ${trip['id']}, Route: ${trip['from_city']} → ${trip['to_city']}, Status: ${trip['status']}');
        }
      }
    } catch (e) {
      print('❌ Trips table test failed: $e');
    }
    
    print('\n🎉 Database function testing completed!');
    
  } catch (e) {
    print('❌ Critical error during testing: $e');
    exit(1);
  }
}
