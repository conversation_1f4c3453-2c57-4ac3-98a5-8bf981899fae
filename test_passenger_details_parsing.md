# Passenger Details Parsing Test Guide

## 🎯 Overview

This guide helps you test the enhanced passenger_details parsing in `booking_request_card.dart` that safely handles both String (JSON) and Map<String, dynamic> formats.

## ✅ Implementation Summary

### Changes Made
1. **Safe JSON Parsing**: Handles both String and Map formats without crashes
2. **Multiple Key Fallbacks**: Supports various key naming conventions
3. **Phone Number Display**: Shows passenger phone when available
4. **Defensive Programming**: Null checks and empty string handling

### Key Methods Added
- `_getPassengerDetails()`: Safe parsing with error handling
- `_getPassengerName()`: Multiple fallback keys for name
- `_getPassengerPhone()`: Multiple fallback keys for phone

## 🧪 Test Cases

### Test Case 1: JSON String Format
**Setup**: Create a booking with passenger_details as JSON string
```json
{"name":"محمد أحمد","phone":"0660123456","profile_image_url":"https://example.com/photo.jpg"}
```

**Expected Result**:
- ✅ Passenger name displays: "محمد أحمد"
- ✅ Phone number displays: "الهاتف: 0660123456"
- ✅ Profile image loads from URL
- ✅ No crashes or exceptions

### Test Case 2: JSONB Map Format
**Setup**: Create a booking with passenger_details as Map in Supabase
```json
{
  "full_name": "فاطمة الزهراء",
  "phone_number": "0661234567",
  "avatar": "https://example.com/avatar.jpg"
}
```

**Expected Result**:
- ✅ Passenger name displays: "فاطمة الزهراء"
- ✅ Phone number displays: "الهاتف: 0661234567"
- ✅ Profile image loads from avatar URL
- ✅ No crashes or exceptions

### Test Case 3: Missing/Null Data
**Setup**: Create a booking with:
- `passenger_details: null`
- `passenger_details: {}`
- `passenger_details: {"other_field": "value"}`

**Expected Result**:
- ✅ Passenger name displays: "مسافر" (fallback)
- ✅ No phone number section shown
- ✅ Default avatar icon displayed
- ✅ No crashes or exceptions

### Test Case 4: Mixed Key Formats
**Setup**: Test different key naming conventions
```json
{
  "fullName": "عبد الله محمد",
  "phoneNumber": "0662345678",
  "profileImageUrl": "https://example.com/pic.jpg"
}
```

**Expected Result**:
- ✅ All fields parsed correctly using camelCase keys
- ✅ Displays properly in UI
- ✅ No crashes or exceptions

## 🔧 How to Test

### Method 1: Direct Database Insert
```sql
-- Insert booking with JSON string passenger_details
INSERT INTO public.bookings (
  trip_id, passenger_id, driver_id, seats_booked, total_price,
  passenger_details, status, created_at, updated_at
) VALUES (
  'your-trip-id',
  'your-passenger-id', 
  'your-driver-id',
  1,
  50.0,
  '{"name":"محمد أحمد","phone":"0660123456"}',
  'pending',
  NOW(),
  NOW()
);
```

### Method 2: App Booking Flow
1. Create a trip as driver
2. Book as passenger (this should store passenger_details)
3. Check driver dashboard for booking request card
4. Verify all fields display correctly

### Method 3: Supabase Dashboard
1. Go to Supabase → Table Editor → bookings
2. Edit a booking row
3. Set passenger_details to different formats:
   - JSON string: `{"name":"Test User","phone":"123456"}`
   - JSONB object: Direct object input
   - Null value: Leave empty
4. Test app display for each format

## 🐛 Common Issues & Solutions

### Issue: "String is not a subtype of Map" Error
**Solution**: ✅ Fixed with safe parsing in `_getPassengerDetails()`

### Issue: Missing passenger name
**Solution**: ✅ Multiple fallback keys: name, full_name, fullName

### Issue: Phone not displaying
**Solution**: ✅ Multiple fallback keys: phone, phone_number, phoneNumber

### Issue: Profile image not loading
**Solution**: ✅ Multiple fallback keys: profile_image_url, profileImageUrl, avatar, image_url

## 📱 Expected UI Behavior

### Booking Request Card Should Show:
1. **Passenger Avatar**: Profile image or default person icon
2. **Passenger Name**: From various name fields or "مسافر" fallback
3. **Rating & Reviews**: From passenger object if available
4. **Phone Number**: "الهاتف: [number]" if available (new feature)
5. **Join Date**: When passenger created account
6. **Trip Details**: Seats, price, route
7. **Message/Requests**: If provided by passenger

### No Crashes Should Occur When:
- passenger_details is null
- passenger_details is empty object {}
- passenger_details is malformed JSON string
- passenger_details has unexpected structure
- Network images fail to load

## ✅ Success Criteria

The implementation is successful when:
- ✅ No TypeError exceptions occur
- ✅ Booking cards display passenger info correctly
- ✅ Phone numbers show when available
- ✅ Fallback values work for missing data
- ✅ Both JSON string and Map formats work
- ✅ UI remains stable with various data formats

## 🔍 Debug Tips

If issues occur, check:
1. **Console Logs**: Look for "⚠️ Error decoding passenger details" messages
2. **Supabase Data**: Verify passenger_details format in database
3. **Network Tab**: Check if profile images are loading
4. **Flutter Inspector**: Examine widget tree for proper data flow

The enhanced implementation provides robust handling of passenger details with comprehensive fallbacks and error handling.
