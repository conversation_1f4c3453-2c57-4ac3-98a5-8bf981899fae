import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';  // Commented out for web compatibility
import 'package:file_picker/file_picker.dart';
import '../../constants/app_theme.dart';

class ImagePickerTest extends StatefulWidget {
  const ImagePickerTest({super.key});

  @override
  State<ImagePickerTest> createState() => _ImagePickerTestState();
}

class _ImagePickerTestState extends State<ImagePickerTest> {
  PlatformFile? _selectedImage;
  bool _isLoading = false;
  String? _error;

  Future<void> _testImagePicker() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final pickedFile = result.files.first;

        // Check file size
        if (pickedFile.bytes != null && pickedFile.bytes!.length > 5 * 1024 * 1024) {
          throw Exception('File too large. Please select an image smaller than 5MB.');
        }

        setState(() {
          _selectedImage = pickedFile;
          _isLoading = false;
        });

        if (kDebugMode) {
          print('✅ Image selected successfully: ${pickedFile.name}');
          print('📏 File size: ${pickedFile.bytes?.length ?? 0} bytes');
          print('🎯 MIME type: ${pickedFile.extension ?? 'unknown'}');
        }
      } else {
        setState(() {
          _isLoading = false;
          _error = 'No file selected';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });

      if (kDebugMode) {
        print('❌ Image picker error: $e');
      }
    }
  }

  String _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      case 'gif':
        return 'image/gif';
      default:
        return 'image/jpeg';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Image Picker Test'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Image preview
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.border, width: 2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: _selectedImage != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: _selectedImage!.bytes != null
                          ? Image.memory(
                              _selectedImage!.bytes!,
                              width: 196,
                              height: 196,
                              fit: BoxFit.cover,
                            )
                          : const Center(
                              child: Text('No image data available'),
                            ),
                    )
                  : const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.image,
                              size: 48, color: AppColors.textSecondary),
                          SizedBox(height: 8),
                          Text('No image selected',
                              style: TextStyle(color: AppColors.textSecondary)),
                        ],
                      ),
                    ),
            ),

            const SizedBox(height: 24),

            // File info
            if (_selectedImage != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('File Name: ${_selectedImage!.name}'),
                      Text(
                          'MIME Type: ${_selectedImage!.extension ?? 'unknown'}'),
                      Text('File Size: ${(_selectedImage!.bytes?.length ?? 0) / 1024} KB'),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Error message
            if (_error != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.error),
                ),
                child: Text(
                  _error!,
                  style: const TextStyle(color: AppColors.error),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Pick image button
            ElevatedButton(
              onPressed: _isLoading ? null : _testImagePicker,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Pick Image'),
            ),

            const SizedBox(height: 16),

            Text(
              kIsWeb
                  ? 'Running on Web - Using ImagePicker (FilePicker disabled)'
                  : 'Running on Mobile - Using ImagePicker',
              style:
                  const TextStyle(color: AppColors.textSecondary, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
