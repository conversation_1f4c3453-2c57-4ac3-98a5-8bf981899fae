import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Create a simple test booking for the existing driver
Future<void> main() async {
  print('🧪 Creating simple test booking...');
  
  try {
    // Initialize Supabase
    await Supabase.initialize(
      url: 'https://pmykpbrecfoxgtahetgb.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBteWtwYnJlY2ZveGd0YWhldGdiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNzQ3ODMsImV4cCI6MjA2NTc1MDc4M30.jxkxOW46KnVWUGR8TClVWMlOevRcwrUSfiZfQ6qMDgE',
    );
    
    final client = Supabase.instance.client;
    print('✅ Supabase client initialized');
    
    // Known driver ID from the console output
    final driverId = '768f205b-6bc2-434c-856c-87c418e2d61d';
    final tripId = '1db7f4db-b209-4365-bda6-c2b9e3a3fddc'; // First trip from console
    
    // Create a test passenger if doesn't exist
    final testPassengerId = '22222222-2222-2222-2222-222222222222';
    
    print('🔍 Checking if test passenger exists...');
    final existingPassenger = await client
        .from('users')
        .select('id, full_name')
        .eq('id', testPassengerId)
        .maybeSingle();
    
    if (existingPassenger == null) {
      print('👤 Creating test passenger...');
      await client
          .from('users')
          .insert({
            'id': testPassengerId,
            'full_name': 'فاطمة المسافرة',
            'role': 'traveler',
            'phone': '+212600000002',
            'city': 'الدار البيضاء',
            'email': '<EMAIL>',
          });
      print('✅ Test passenger created');
    } else {
      print('✅ Test passenger already exists: ${existingPassenger['full_name']}');
    }
    
    // Create a pending booking
    print('📋 Creating pending booking...');
    try {
      final booking = await client
          .from('bookings')
          .insert({
            'trip_id': tripId,
            'passenger_id': testPassengerId,
            'driver_id': driverId,
            'seats_booked': 1,
            'total_price': 220.0,
            'status': 'pending',
            'booking_type': 'manual',
            'message': 'مرحبا، أريد حجز مقعد في هذه الرحلة من فضلك',
            'special_requests': 'أفضل المقعد الأمامي',
            'passenger_details': {
              'name': 'فاطمة المسافرة',
              'phone': '+212600000002',
              'has_luggage': true,
            },
          })
          .select()
          .single();
      
      print('✅ Pending booking created successfully!');
      print('   Booking ID: ${booking['id']}');
      print('   Trip: ${booking['trip_id']}');
      print('   Passenger: ${booking['passenger_id']}');
      print('   Driver: ${booking['driver_id']}');
      print('   Status: ${booking['status']}');
      
    } catch (e) {
      print('⚠️ Booking creation failed (may already exist): $e');
    }
    
    // Create another accepted booking for testing
    print('📋 Creating accepted booking...');
    try {
      final acceptedBooking = await client
          .from('bookings')
          .insert({
            'trip_id': tripId,
            'passenger_id': testPassengerId,
            'driver_id': driverId,
            'seats_booked': 1,
            'total_price': 220.0,
            'status': 'accepted',
            'booking_type': 'instant',
            'message': 'حجز فوري',
            'confirmed_at': DateTime.now().toIso8601String(),
            'passenger_details': {
              'name': 'فاطمة المسافرة',
              'phone': '+212600000002',
              'has_luggage': false,
            },
          })
          .select()
          .single();
      
      print('✅ Accepted booking created successfully!');
      print('   Booking ID: ${acceptedBooking['id']}');
      
    } catch (e) {
      print('⚠️ Accepted booking creation failed (may already exist): $e');
    }
    
    print('\n🎉 Test booking creation completed!');
    print('📱 Now check the driver dashboard to see the bookings.');
    
  } catch (e) {
    print('❌ Error: $e');
  }
  
  exit(0);
}
