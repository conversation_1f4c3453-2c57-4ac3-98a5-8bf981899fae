-- =====================================================
-- Safarni Booking System Migration
-- Real-time Trip Booking, Chat, and Notifications
-- =====================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. BOOKINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL REFERENCES public.trips(id) ON DELETE CASCADE,
    passenger_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    driver_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Booking details
    seats_booked INTEGER NOT NULL DEFAULT 1 CHECK (seats_booked > 0),
    total_price DECIMAL(10,2) NOT NULL CHECK (total_price >= 0),
    booking_type TEXT NOT NULL DEFAULT 'manual' CHECK (booking_type IN ('instant', 'manual')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'completed', 'cancelled')),
    
    -- Additional info
    message TEXT,
    rejection_reason TEXT,
    special_requests TEXT,
    passenger_details JSONB DEFAULT '{}',
    
    -- Payment info
    is_paid BOOLEAN DEFAULT FALSE,
    payment_method TEXT,
    payment_reference TEXT,
    
    -- Timestamps
    confirmed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. MESSAGES TABLE (Chat System)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL REFERENCES public.trips(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    receiver_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Message content
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'audio', 'location', 'image', 'system')),
    
    -- Location data (for location messages)
    location_data JSONB,
    
    -- Message status
    is_read BOOLEAN DEFAULT FALSE,
    is_delivered BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. NOTIFICATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Notification content
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    notification_type TEXT NOT NULL DEFAULT 'general' CHECK (notification_type IN (
        'booking_request', 'booking_accepted', 'booking_rejected', 
        'trip_reminder', 'message', 'trip_update', 'general'
    )),
    
    -- Related entities
    trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
    related_user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Notification data
    data JSONB DEFAULT '{}',
    
    -- Status
    is_read BOOLEAN DEFAULT FALSE,
    is_sent BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- 4. CONVERSATIONS TABLE (Chat Organization)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL REFERENCES public.trips(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
    driver_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    passenger_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Conversation metadata
    last_message_id UUID REFERENCES public.messages(id),
    last_message_at TIMESTAMP WITH TIME ZONE,
    unread_count_driver INTEGER DEFAULT 0,
    unread_count_passenger INTEGER DEFAULT 0,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique conversation per trip-passenger pair
    UNIQUE(trip_id, passenger_id)
);

-- =====================================================
-- 5. INDEXES FOR PERFORMANCE
-- =====================================================

-- Bookings indexes
CREATE INDEX IF NOT EXISTS idx_bookings_trip_id ON public.bookings(trip_id);
CREATE INDEX IF NOT EXISTS idx_bookings_passenger_id ON public.bookings(passenger_id);
CREATE INDEX IF NOT EXISTS idx_bookings_driver_id ON public.bookings(driver_id);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON public.bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON public.bookings(created_at DESC);

-- Messages indexes
CREATE INDEX IF NOT EXISTS idx_messages_trip_id ON public.messages(trip_id);
CREATE INDEX IF NOT EXISTS idx_messages_booking_id ON public.messages(booking_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_receiver_id ON public.messages(receiver_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at DESC);

-- Notifications indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at DESC);

-- Conversations indexes
CREATE INDEX IF NOT EXISTS idx_conversations_trip_id ON public.conversations(trip_id);
CREATE INDEX IF NOT EXISTS idx_conversations_driver_id ON public.conversations(driver_id);
CREATE INDEX IF NOT EXISTS idx_conversations_passenger_id ON public.conversations(passenger_id);
CREATE INDEX IF NOT EXISTS idx_conversations_last_message_at ON public.conversations(last_message_at DESC);

-- =====================================================
-- 6. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;

-- Bookings RLS Policies
CREATE POLICY "Users can view their own bookings" ON public.bookings
    FOR SELECT USING (passenger_id = auth.uid() OR driver_id = auth.uid());

CREATE POLICY "Passengers can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (passenger_id = auth.uid());

CREATE POLICY "Drivers and passengers can update their bookings" ON public.bookings
    FOR UPDATE USING (passenger_id = auth.uid() OR driver_id = auth.uid());

-- Messages RLS Policies
CREATE POLICY "Users can view their own messages" ON public.messages
    FOR SELECT USING (sender_id = auth.uid() OR receiver_id = auth.uid());

CREATE POLICY "Users can send messages" ON public.messages
    FOR INSERT WITH CHECK (sender_id = auth.uid());

CREATE POLICY "Users can update their own messages" ON public.messages
    FOR UPDATE USING (sender_id = auth.uid());

-- Notifications RLS Policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (user_id = auth.uid());

-- Conversations RLS Policies
CREATE POLICY "Users can view their own conversations" ON public.conversations
    FOR SELECT USING (driver_id = auth.uid() OR passenger_id = auth.uid());

CREATE POLICY "System can create conversations" ON public.conversations
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own conversations" ON public.conversations
    FOR UPDATE USING (driver_id = auth.uid() OR passenger_id = auth.uid());

-- =====================================================
-- 7. TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update updated_at timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON public.bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON public.conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 8. FUNCTIONS FOR BUSINESS LOGIC
-- =====================================================

-- Function to create conversation when booking is accepted
CREATE OR REPLACE FUNCTION create_conversation_on_booking_acceptance()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create conversation when booking status changes to 'accepted'
    IF NEW.status = 'accepted' AND OLD.status != 'accepted' THEN
        INSERT INTO public.conversations (trip_id, booking_id, driver_id, passenger_id)
        VALUES (NEW.trip_id, NEW.id, NEW.driver_id, NEW.passenger_id)
        ON CONFLICT (trip_id, passenger_id) DO NOTHING;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_create_conversation_on_booking_acceptance
    AFTER UPDATE ON public.bookings
    FOR EACH ROW EXECUTE FUNCTION create_conversation_on_booking_acceptance();

-- Function to update trip available seats when booking status changes
CREATE OR REPLACE FUNCTION update_trip_seats_on_booking_change()
RETURNS TRIGGER AS $$
BEGIN
    -- When booking is accepted, decrease available seats
    IF NEW.status = 'accepted' AND OLD.status != 'accepted' THEN
        UPDATE public.trips 
        SET available_seats = available_seats - NEW.seats_booked
        WHERE id = NEW.trip_id AND available_seats >= NEW.seats_booked;
    END IF;
    
    -- When booking is rejected or cancelled, increase available seats
    IF (NEW.status IN ('rejected', 'cancelled')) AND (OLD.status = 'accepted') THEN
        UPDATE public.trips 
        SET available_seats = available_seats + NEW.seats_booked
        WHERE id = NEW.trip_id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_trip_seats_on_booking_change
    AFTER UPDATE ON public.bookings
    FOR EACH ROW EXECUTE FUNCTION update_trip_seats_on_booking_change();

-- =====================================================
-- 9. SAMPLE DATA FOR TESTING
-- =====================================================

-- Note: Sample data will be inserted through the app
-- This ensures proper authentication context for RLS policies

-- =====================================================
-- 10. RATINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL REFERENCES public.trips(id) ON DELETE CASCADE,
    booking_id UUID NOT NULL REFERENCES public.bookings(id) ON DELETE CASCADE,
    rater_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    rated_user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,

    -- Rating details
    rating DECIMAL(2,1) NOT NULL CHECK (rating >= 1.0 AND rating <= 5.0),
    comment TEXT,
    rating_type TEXT NOT NULL CHECK (rating_type IN ('driver_to_passenger', 'passenger_to_driver')),

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique rating per trip/booking/rater/type combination
    UNIQUE(trip_id, booking_id, rater_id, rating_type)
);

-- Ratings indexes
CREATE INDEX IF NOT EXISTS idx_ratings_trip_id ON public.ratings(trip_id);
CREATE INDEX IF NOT EXISTS idx_ratings_booking_id ON public.ratings(booking_id);
CREATE INDEX IF NOT EXISTS idx_ratings_rater_id ON public.ratings(rater_id);
CREATE INDEX IF NOT EXISTS idx_ratings_rated_user_id ON public.ratings(rated_user_id);
CREATE INDEX IF NOT EXISTS idx_ratings_created_at ON public.ratings(created_at DESC);

-- Enable RLS on ratings table
ALTER TABLE public.ratings ENABLE ROW LEVEL SECURITY;

-- Ratings RLS Policies
CREATE POLICY "Users can view ratings they gave or received" ON public.ratings
    FOR SELECT USING (rater_id = auth.uid() OR rated_user_id = auth.uid());

CREATE POLICY "Users can create ratings for their trips" ON public.ratings
    FOR INSERT WITH CHECK (rater_id = auth.uid());

CREATE POLICY "Users can update their own ratings" ON public.ratings
    FOR UPDATE USING (rater_id = auth.uid());

-- Apply update trigger to ratings
CREATE TRIGGER update_ratings_updated_at BEFORE UPDATE ON public.ratings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update user rating when new rating is added
CREATE OR REPLACE FUNCTION update_user_rating_on_new_rating()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the rated user's average rating and total ratings
    UPDATE public.users
    SET
        rating = (
            SELECT AVG(rating)
            FROM public.ratings
            WHERE rated_user_id = NEW.rated_user_id
        ),
        total_ratings = (
            SELECT COUNT(*)
            FROM public.ratings
            WHERE rated_user_id = NEW.rated_user_id
        )
    WHERE id = NEW.rated_user_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_user_rating_on_new_rating
    AFTER INSERT ON public.ratings
    FOR EACH ROW EXECUTE FUNCTION update_user_rating_on_new_rating();

-- Function to update user rating when rating is updated
CREATE OR REPLACE FUNCTION update_user_rating_on_rating_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the rated user's average rating and total ratings
    UPDATE public.users
    SET
        rating = (
            SELECT AVG(rating)
            FROM public.ratings
            WHERE rated_user_id = NEW.rated_user_id
        ),
        total_ratings = (
            SELECT COUNT(*)
            FROM public.ratings
            WHERE rated_user_id = NEW.rated_user_id
        )
    WHERE id = NEW.rated_user_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_user_rating_on_rating_change
    AFTER UPDATE ON public.ratings
    FOR EACH ROW EXECUTE FUNCTION update_user_rating_on_rating_change();

COMMENT ON TABLE public.bookings IS 'Trip booking requests and confirmations';
COMMENT ON TABLE public.messages IS 'Chat messages between drivers and passengers';
COMMENT ON TABLE public.notifications IS 'In-app notifications for users';
COMMENT ON TABLE public.conversations IS 'Chat conversations organized by trip and participants';
COMMENT ON TABLE public.ratings IS 'User ratings and reviews for completed trips';
