import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../models/message_model.dart' hide ConversationModel;
import '../../models/conversation_model.dart';
import '../../models/trip_model.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/message_service.dart';
import 'message_bubble.dart';
import 'chat_input.dart';

class ChatPage extends StatefulWidget {
  final String tripId;
  final String otherUserId;
  final String? conversationId;

  const ChatPage({
    super.key,
    required this.tripId,
    required this.otherUserId,
    this.conversationId,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final List<MessageModel> _messages = [];

  UserModel? _otherUser;
  TripModel? _trip;
  ConversationModel? _conversation;
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _isTyping = false;

  RealtimeChannel? _messageChannel;
  late AnimationController _typingAnimationController;
  late Animation<double> _typingAnimation;

  static const int _messagesPerPage = 50;
  int _currentOffset = 0;
  bool _hasMoreMessages = true;

  @override
  void initState() {
    super.initState();
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _typingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingAnimationController,
      curve: Curves.easeInOut,
    ));

    _loadInitialData();
    _setupScrollListener();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _typingAnimationController.dispose();
    _cleanupSubscriptions();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    try {
      // Load conversation or create if doesn't exist
      final conversation = await MessageService.getOrCreateConversation(
        tripId: widget.tripId,
        driverId: widget.otherUserId, // We'll determine roles properly
        passengerId: currentUser.id,
      );

      if (conversation != null) {
        _conversation = conversation;
        
        // Load initial messages
        await _loadMessages();
        
        // Setup real-time subscription
        _setupRealtimeSubscription();
        
        // Mark messages as read
        await MessageService.markMessagesAsRead(
          tripId: widget.tripId,
          receiverId: currentUser.id,
          senderId: widget.otherUserId,
        );
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل المحادثة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _loadMessages() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    try {
      final messages = await MessageService.getConversationMessages(
        tripId: widget.tripId,
        userId1: currentUser.id,
        userId2: widget.otherUserId,
        limit: _messagesPerPage,
        offset: _currentOffset,
      );

      if (mounted) {
        setState(() {
          if (_currentOffset == 0) {
            _messages.clear();
          }
          _messages.addAll(messages.reversed); // Add in chronological order
          _currentOffset += messages.length;
          _hasMoreMessages = messages.length == _messagesPerPage;
        });

        // Scroll to bottom for initial load
        if (_currentOffset == messages.length) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل الرسائل: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      // Load more messages when scrolling to top
      if (_scrollController.position.pixels == 0 && 
          _hasMoreMessages && 
          !_isLoadingMore) {
        _loadMoreMessages();
      }
    });
  }

  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore || !_hasMoreMessages) return;

    setState(() {
      _isLoadingMore = true;
    });

    await _loadMessages();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  void _setupRealtimeSubscription() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    _messageChannel = MessageService.subscribeToConversationMessages(
      tripId: widget.tripId,
      userId: currentUser.id,
      onMessageReceived: (message) {
        if (mounted) {
          setState(() {
            _messages.add(message);
          });
          
          // Scroll to bottom for new messages
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });

          // Mark as read if message is for current user
          if (message.receiverId == currentUser.id) {
            MessageService.markMessagesAsRead(
              tripId: widget.tripId,
              receiverId: currentUser.id,
              senderId: message.senderId,
            );
          }
        }
      },
    );
  }

  void _cleanupSubscriptions() {
    if (_messageChannel != null) {
      MessageService.unsubscribeFromMessages(_messageChannel!);
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> _sendMessage(String content, {String messageType = 'text'}) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null || content.trim().isEmpty) return;

    try {
      await MessageService.sendMessage(
        tripId: widget.tripId,
        bookingId: _conversation?.bookingId,
        senderId: currentUser.id,
        receiverId: widget.otherUserId,
        content: content.trim(),
        messageType: messageType,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إرسال الرسالة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _sendLocationMessage(double latitude, double longitude, String? address) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    try {
      await MessageService.sendLocationMessage(
        tripId: widget.tripId,
        bookingId: _conversation?.bookingId,
        senderId: currentUser.id,
        receiverId: widget.otherUserId,
        latitude: latitude,
        longitude: longitude,
        address: address,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إرسال الموقع: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: ClipOval(
                child: _otherUser?.profileImageUrl != null
                    ? Image.network(
                        _otherUser!.profileImageUrl!,
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppColors.primary.withValues(alpha: 0.2),
                            child: Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 20,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: AppColors.primary.withValues(alpha: 0.2),
                        child: Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _otherUser?.fullName ?? 'مستخدم',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (_trip != null)
                    Text(
                      '${_trip!.fromCity} ← ${_trip!.toCity}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        shadowColor: AppColors.shadow.withValues(alpha: 0.3),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement voice call
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('المكالمات الصوتية قيد التطوير'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            icon: const Icon(Icons.call, color: Colors.white),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) {
              switch (value) {
                case 'trip_details':
                  // TODO: Show trip details
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تفاصيل الرحلة قيد التطوير'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                  break;
                case 'block':
                  // TODO: Implement block user
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('حظر المستخدم قيد التطوير'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'trip_details',
                child: Row(
                  children: [
                    Icon(Icons.info_outline, size: 20),
                    SizedBox(width: 8),
                    Text('تفاصيل الرحلة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'block',
                child: Row(
                  children: [
                    Icon(Icons.block, size: 20),
                    SizedBox(width: 8),
                    Text('حظر المستخدم'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Messages List
                Expanded(
                  child: Stack(
                    children: [
                      ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _messages.length +
                            (_isLoadingMore ? 1 : 0) +
                            (_isTyping ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == 0 && _isLoadingMore) {
                            return const Center(
                              child: Padding(
                                padding: EdgeInsets.all(16),
                                child: CircularProgressIndicator(),
                              ),
                            );
                          }

                          // Adjust index for loading indicator
                          final adjustedIndex = _isLoadingMore ? index - 1 : index;

                          // Show typing indicator at the end
                          if (_isTyping && adjustedIndex == _messages.length) {
                            return _buildTypingIndicator();
                          }

                          // Show regular message
                          if (adjustedIndex < _messages.length) {
                            final message = _messages[adjustedIndex];

                            return AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                              child: MessageBubble(
                                message: message,
                                isMe: message.senderId == Provider.of<AuthProvider>(context, listen: false).currentUser?.id,
                              ),
                            );
                          }

                          return const SizedBox.shrink();
                        },
                      ),

                      // Scroll to bottom button
                      if (_messages.isNotEmpty)
                        Positioned(
                          bottom: 16,
                          right: 16,
                          child: _buildScrollToBottomButton(),
                        ),
                    ],
                  ),
                ),
                
                // Chat Input
                ChatInput(
                  onSendMessage: _sendMessage,
                  onSendLocation: _sendLocationMessage,
                ),
              ],
            ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundImage: _otherUser?.profileImageUrl != null
                ? NetworkImage(_otherUser!.profileImageUrl!)
                : null,
            child: _otherUser?.profileImageUrl == null
                ? const Icon(Icons.person, size: 16)
                : null,
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: AnimatedBuilder(
              animation: _typingAnimation,
              builder: (context, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTypingDot(0),
                    const SizedBox(width: 4),
                    _buildTypingDot(1),
                    const SizedBox(width: 4),
                    _buildTypingDot(2),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingDot(int index) {
    final delay = index * 0.2;
    final animationValue = (_typingAnimation.value - delay).clamp(0.0, 1.0);
    final opacity = (animationValue * 2).clamp(0.0, 1.0);

    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: AppColors.textSecondary.withValues(alpha: opacity),
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildScrollToBottomButton() {
    return AnimatedBuilder(
      animation: _scrollController,
      builder: (context, child) {
        final showButton = _scrollController.hasClients &&
            _scrollController.offset > 100;

        return AnimatedScale(
          scale: showButton ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: FloatingActionButton.small(
            onPressed: _scrollToBottom,
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            child: const Icon(Icons.keyboard_arrow_down),
          ),
        );
      },
    );
  }

  void _startTypingAnimation() {
    if (!_isTyping) {
      setState(() {
        _isTyping = true;
      });
      _typingAnimationController.repeat();
    }
  }

  void _stopTypingAnimation() {
    if (_isTyping) {
      setState(() {
        _isTyping = false;
      });
      _typingAnimationController.stop();
    }
  }
}
