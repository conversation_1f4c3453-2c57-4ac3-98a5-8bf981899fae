import 'package:flutter/material.dart';

/// Utility class for common animations used throughout the app
class AnimationUtils {
  // Animation durations
  static const Duration fastDuration = Duration(milliseconds: 200);
  static const Duration normalDuration = Duration(milliseconds: 300);
  static const Duration slowDuration = Duration(milliseconds: 500);

  // Animation curves
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve slideCurve = Curves.easeOutCubic;

  /// Creates a slide transition from bottom to top
  static Widget slideFromBottom({
    required Widget child,
    required AnimationController controller,
    Duration delay = Duration.zero,
  }) {
    final animation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay.inMilliseconds / controller.duration!.inMilliseconds,
        1.0,
        curve: slideCurve,
      ),
    ));

    return SlideTransition(
      position: animation,
      child: child,
    );
  }

  /// Creates a slide transition from right to left (RTL support)
  static Widget slideFromRight({
    required Widget child,
    required AnimationController controller,
    Duration delay = Duration.zero,
  }) {
    final animation = Tween<Offset>(
      begin: const Offset(1, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay.inMilliseconds / controller.duration!.inMilliseconds,
        1.0,
        curve: slideCurve,
      ),
    ));

    return SlideTransition(
      position: animation,
      child: child,
    );
  }

  /// Creates a fade and scale transition
  static Widget fadeAndScale({
    required Widget child,
    required AnimationController controller,
    Duration delay = Duration.zero,
    double initialScale = 0.8,
  }) {
    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay.inMilliseconds / controller.duration!.inMilliseconds,
        1.0,
        curve: defaultCurve,
      ),
    ));

    final scaleAnimation = Tween<double>(
      begin: initialScale,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay.inMilliseconds / controller.duration!.inMilliseconds,
        1.0,
        curve: bounceCurve,
      ),
    ));

    return FadeTransition(
      opacity: fadeAnimation,
      child: ScaleTransition(
        scale: scaleAnimation,
        child: child,
      ),
    );
  }

  /// Creates a staggered list animation
  static Widget staggeredListItem({
    required Widget child,
    required int index,
    required AnimationController controller,
    Duration itemDelay = const Duration(milliseconds: 100),
  }) {
    final delay = itemDelay * index;
    return fadeAndScale(
      child: child,
      controller: controller,
      delay: delay,
    );
  }

  /// Creates a ripple effect animation
  static Widget rippleEffect({
    required Widget child,
    required VoidCallback onTap,
    Color? rippleColor,
    BorderRadius? borderRadius,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        splashColor: rippleColor?.withValues(alpha: 0.3),
        highlightColor: rippleColor?.withValues(alpha: 0.1),
        borderRadius: borderRadius,
        child: child,
      ),
    );
  }

  /// Creates a shimmer loading effect
  static Widget shimmerLoading({
    required Widget child,
    bool isLoading = true,
    Color? baseColor,
    Color? highlightColor,
  }) {
    if (!isLoading) return child;

    return AnimatedContainer(
      duration: normalDuration,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            baseColor ?? Colors.grey[300]!,
            highlightColor ?? Colors.grey[100]!,
            baseColor ?? Colors.grey[300]!,
          ],
          stops: const [0.0, 0.5, 1.0],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: child,
    );
  }

  /// Creates a status change animation
  static Widget statusChangeAnimation({
    required Widget child,
    required String status,
    required String previousStatus,
  }) {
    return TweenAnimationBuilder<double>(
      duration: normalDuration,
      tween: Tween<double>(begin: 0.0, end: 1.0),
      curve: bounceCurve,
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// Creates a floating action button animation
  static Widget floatingButtonAnimation({
    required Widget child,
    required bool isVisible,
  }) {
    return AnimatedScale(
      scale: isVisible ? 1.0 : 0.0,
      duration: normalDuration,
      curve: bounceCurve,
      child: AnimatedOpacity(
        opacity: isVisible ? 1.0 : 0.0,
        duration: fastDuration,
        child: child,
      ),
    );
  }

  /// Creates a card flip animation
  static Widget cardFlip({
    required Widget front,
    required Widget back,
    required bool showFront,
    required AnimationController controller,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final isShowingFront = controller.value < 0.5;
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(controller.value * 3.14159),
          child: isShowingFront ? front : back,
        );
      },
    );
  }

  /// Creates a progress indicator animation
  static Widget progressIndicator({
    required double progress,
    Color? color,
    Color? backgroundColor,
    double height = 4.0,
    BorderRadius? borderRadius,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey[300],
        borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress.clamp(0.0, 1.0),
        child: AnimatedContainer(
          duration: normalDuration,
          decoration: BoxDecoration(
            color: color ?? Colors.blue,
            borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
          ),
        ),
      ),
    );
  }

  /// Creates a bounce animation for buttons
  static Widget bounceButton({
    required Widget child,
    required VoidCallback onPressed,
    Duration duration = const Duration(milliseconds: 150),
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween<double>(begin: 1.0, end: 1.0),
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: GestureDetector(
            onTapDown: (_) {
              // Trigger scale down animation
            },
            onTapUp: (_) {
              onPressed();
              // Trigger scale up animation
            },
            onTapCancel: () {
              // Reset scale
            },
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// Creates a typing indicator animation (for chat)
  static Widget typingIndicator({
    required AnimationController controller,
    Color? color,
    double size = 8.0,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: controller,
          builder: (context, child) {
            final delay = index * 0.2;
            final animationValue = (controller.value - delay).clamp(0.0, 1.0);
            final opacity = (animationValue * 2).clamp(0.0, 1.0);
            
            return Container(
              margin: EdgeInsets.symmetric(horizontal: size * 0.25),
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: (color ?? Colors.grey).withValues(alpha: opacity),
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }
}

/// Extension for easy animation access on widgets
extension AnimationExtensions on Widget {
  Widget fadeIn({
    Duration duration = AnimationUtils.normalDuration,
    Duration delay = Duration.zero,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration + delay,
      tween: Tween<double>(begin: 0.0, end: 1.0),
      builder: (context, opacity, child) {
        return Opacity(
          opacity: opacity,
          child: child,
        );
      },
      child: this,
    );
  }

  Widget slideInFromBottom({
    Duration duration = AnimationUtils.normalDuration,
    Duration delay = Duration.zero,
  }) {
    return TweenAnimationBuilder<Offset>(
      duration: duration + delay,
      tween: Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero),
      builder: (context, offset, child) {
        return Transform.translate(
          offset: Offset(0, offset.dy * 50),
          child: child,
        );
      },
      child: this,
    );
  }

  Widget scaleIn({
    Duration duration = AnimationUtils.normalDuration,
    Duration delay = Duration.zero,
    double initialScale = 0.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration + delay,
      tween: Tween<double>(begin: initialScale, end: 1.0),
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: child,
        );
      },
      child: this,
    );
  }
}
