-- =====================================================
-- DRIVER BOOKINGS FIX - DATABASE SCHEMA UPDATES
-- =====================================================

-- Step 1: Check current bookings table structure
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name='bookings' 
ORDER BY ordinal_position;

-- Step 2: Add missing columns safely
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS driver_id uuid;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS passenger_id uuid;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS booking_type text DEFAULT 'manual';
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS message text;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS special_requests text;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS passenger_details jsonb;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS rejection_reason text;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS total_price decimal(8,2);
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS is_paid boolean DEFAULT false;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS payment_method text;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS payment_reference text;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS completed_at timestamptz;

-- Step 3: Update status check constraint to include all expected values
ALTER TABLE public.bookings DROP CONSTRAINT IF EXISTS bookings_status_check;
ALTER TABLE public.bookings ADD CONSTRAINT bookings_status_check 
CHECK (status IN ('pending', 'accepted', 'rejected', 'confirmed', 'cancelled', 'completed'));

-- Step 4: Migrate existing data if traveler_id exists
-- Copy traveler_id to passenger_id for existing records
UPDATE public.bookings 
SET passenger_id = traveler_id 
WHERE passenger_id IS NULL AND traveler_id IS NOT NULL;

-- Copy total_amount to total_price for existing records  
UPDATE public.bookings 
SET total_price = total_amount 
WHERE total_price IS NULL AND total_amount IS NOT NULL;

-- Step 5: Set driver_id from trips table for existing bookings
UPDATE public.bookings 
SET driver_id = trips.leader_id
FROM public.trips 
WHERE bookings.trip_id = trips.id 
AND bookings.driver_id IS NULL;

-- Step 6: Add foreign key constraints for new columns
ALTER TABLE public.bookings ADD CONSTRAINT fk_bookings_driver 
FOREIGN KEY (driver_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.bookings ADD CONSTRAINT fk_bookings_passenger 
FOREIGN KEY (passenger_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Step 7: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_driver_id ON public.bookings(driver_id);
CREATE INDEX IF NOT EXISTS idx_bookings_passenger_id ON public.bookings(passenger_id);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON public.bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_driver_status ON public.bookings(driver_id, status);

-- Step 8: Verify the changes
SELECT 
    COUNT(*) as total_bookings,
    COUNT(driver_id) as bookings_with_driver,
    COUNT(passenger_id) as bookings_with_passenger,
    COUNT(DISTINCT status) as unique_statuses
FROM public.bookings;

-- Show sample of updated data
SELECT 
    id, 
    trip_id, 
    driver_id, 
    passenger_id, 
    status, 
    total_price,
    booking_type,
    created_at
FROM public.bookings 
LIMIT 5;
