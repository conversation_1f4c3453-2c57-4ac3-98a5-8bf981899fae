import 'user_model.dart';

class NotificationModel {
  final String id;
  final String userId;
  final String title;
  final String body;
  final String notificationType; // 'booking_request', 'booking_accepted', 'booking_rejected', 'trip_reminder', 'message', 'trip_update', 'general'
  final String? tripId;
  final String? bookingId;
  final String? relatedUserId;
  final UserModel? relatedUser;
  final Map<String, dynamic> data;
  final bool isRead;
  final bool isSent;
  final DateTime createdAt;
  final DateTime? readAt;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    this.notificationType = 'general',
    this.tripId,
    this.bookingId,
    this.relatedUserId,
    this.relatedUser,
    this.data = const {},
    this.isRead = false,
    this.isSent = false,
    required this.createdAt,
    this.readAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      notificationType: json['notification_type'] as String? ?? 'general',
      tripId: json['trip_id'] as String?,
      bookingId: json['booking_id'] as String?,
      relatedUserId: json['related_user_id'] as String?,
      relatedUser: json['related_user'] != null 
          ? UserModel.fromJson(json['related_user'] as Map<String, dynamic>)
          : null,
      data: json['data'] as Map<String, dynamic>? ?? {},
      isRead: json['is_read'] as bool? ?? false,
      isSent: json['is_sent'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      readAt: json['read_at'] != null 
          ? DateTime.parse(json['read_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'body': body,
      'notification_type': notificationType,
      'trip_id': tripId,
      'booking_id': bookingId,
      'related_user_id': relatedUserId,
      'data': data,
      'is_read': isRead,
      'is_sent': isSent,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    String? notificationType,
    String? tripId,
    String? bookingId,
    String? relatedUserId,
    UserModel? relatedUser,
    Map<String, dynamic>? data,
    bool? isRead,
    bool? isSent,
    DateTime? createdAt,
    DateTime? readAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      notificationType: notificationType ?? this.notificationType,
      tripId: tripId ?? this.tripId,
      bookingId: bookingId ?? this.bookingId,
      relatedUserId: relatedUserId ?? this.relatedUserId,
      relatedUser: relatedUser ?? this.relatedUser,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      isSent: isSent ?? this.isSent,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
    );
  }

  // Helper methods
  bool get isBookingRequest => notificationType == 'booking_request';
  bool get isBookingAccepted => notificationType == 'booking_accepted';
  bool get isBookingRejected => notificationType == 'booking_rejected';
  bool get isTripReminder => notificationType == 'trip_reminder';
  bool get isMessage => notificationType == 'message';
  bool get isTripUpdate => notificationType == 'trip_update';

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return 'منذ ${(difference.inDays / 7).floor()} أسبوع';
    }
  }

  // Factory methods for creating specific notification types
  static NotificationModel createBookingRequest({
    required String userId,
    required String tripId,
    required String bookingId,
    required String passengerName,
    required String tripDestination,
    String? relatedUserId,
  }) {
    return NotificationModel(
      id: '', // Will be generated by Supabase
      userId: userId,
      title: 'طلب حجز جديد',
      body: 'طلب حجز جديد من $passengerName لرحلة $tripDestination',
      notificationType: 'booking_request',
      tripId: tripId,
      bookingId: bookingId,
      relatedUserId: relatedUserId,
      data: {
        'passenger_name': passengerName,
        'trip_destination': tripDestination,
      },
      createdAt: DateTime.now(),
    );
  }

  static NotificationModel createBookingAccepted({
    required String userId,
    required String tripId,
    required String bookingId,
    required String driverName,
    required String tripDestination,
    String? relatedUserId,
  }) {
    return NotificationModel(
      id: '', // Will be generated by Supabase
      userId: userId,
      title: 'تم قبول الحجز',
      body: 'تم قبول حجزك من $driverName لرحلة $tripDestination',
      notificationType: 'booking_accepted',
      tripId: tripId,
      bookingId: bookingId,
      relatedUserId: relatedUserId,
      data: {
        'driver_name': driverName,
        'trip_destination': tripDestination,
      },
      createdAt: DateTime.now(),
    );
  }

  static NotificationModel createBookingRejected({
    required String userId,
    required String tripId,
    required String bookingId,
    required String driverName,
    required String tripDestination,
    String? rejectionReason,
    String? relatedUserId,
  }) {
    return NotificationModel(
      id: '', // Will be generated by Supabase
      userId: userId,
      title: 'تم رفض الحجز',
      body: 'تم رفض حجزك من $driverName لرحلة $tripDestination${rejectionReason != null ? ': $rejectionReason' : ''}',
      notificationType: 'booking_rejected',
      tripId: tripId,
      bookingId: bookingId,
      relatedUserId: relatedUserId,
      data: {
        'driver_name': driverName,
        'trip_destination': tripDestination,
        'rejection_reason': rejectionReason,
      },
      createdAt: DateTime.now(),
    );
  }

  static NotificationModel createTripReminder({
    required String userId,
    required String tripId,
    required String tripDestination,
    required DateTime departureTime,
    required String reminderType, // '24h', '30min'
  }) {
    final timeText = reminderType == '24h' ? 'غداً' : 'خلال 30 دقيقة';
    return NotificationModel(
      id: '', // Will be generated by Supabase
      userId: userId,
      title: 'تذكير بالرحلة',
      body: 'رحلتك إلى $tripDestination ستبدأ $timeText',
      notificationType: 'trip_reminder',
      tripId: tripId,
      data: {
        'trip_destination': tripDestination,
        'departure_time': departureTime.toIso8601String(),
        'reminder_type': reminderType,
      },
      createdAt: DateTime.now(),
    );
  }

  static NotificationModel createNewMessage({
    required String userId,
    required String tripId,
    required String bookingId,
    required String senderName,
    required String messagePreview,
    String? relatedUserId,
  }) {
    return NotificationModel(
      id: '', // Will be generated by Supabase
      userId: userId,
      title: 'رسالة جديدة من $senderName',
      body: messagePreview,
      notificationType: 'message',
      tripId: tripId,
      bookingId: bookingId,
      relatedUserId: relatedUserId,
      data: {
        'sender_name': senderName,
        'message_preview': messagePreview,
      },
      createdAt: DateTime.now(),
    );
  }
}
