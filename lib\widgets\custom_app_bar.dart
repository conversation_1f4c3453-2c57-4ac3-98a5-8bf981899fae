import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final bool automaticallyImplyLeading;
  final VoidCallback? onBackPressed;
  final PreferredSizeWidget? bottom;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 2,
    this.automaticallyImplyLeading = true,
    this.onBackPressed,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: foregroundColor ?? Colors.white,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation,
      shadowColor: AppColors.shadow.withValues(alpha: 0.3),
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading ?? (automaticallyImplyLeading && Navigator.canPop(context)
          ? IconButton(
              onPressed: onBackPressed ?? () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back_ios),
              tooltip: 'رجوع',
            )
          : null),
      actions: actions,
      bottom: bottom,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(0),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0),
      );

  // Factory constructors for common app bar styles
  factory CustomAppBar.primary({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
  }) {
    return CustomAppBar(
      title: title,
      actions: actions,
      leading: leading,
      onBackPressed: onBackPressed,
      bottom: bottom,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
    );
  }

  factory CustomAppBar.secondary({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
  }) {
    return CustomAppBar(
      title: title,
      actions: actions,
      leading: leading,
      onBackPressed: onBackPressed,
      bottom: bottom,
      backgroundColor: AppColors.secondary,
      foregroundColor: Colors.white,
    );
  }

  factory CustomAppBar.surface({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
  }) {
    return CustomAppBar(
      title: title,
      actions: actions,
      leading: leading,
      onBackPressed: onBackPressed,
      bottom: bottom,
      backgroundColor: AppColors.surface,
      foregroundColor: AppColors.textPrimary,
      elevation: 1,
    );
  }

  factory CustomAppBar.transparent({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
  }) {
    return CustomAppBar(
      title: title,
      actions: actions,
      leading: leading,
      onBackPressed: onBackPressed,
      bottom: bottom,
      backgroundColor: Colors.transparent,
      foregroundColor: AppColors.textPrimary,
      elevation: 0,
    );
  }

  // Chat-specific app bar with user info
  factory CustomAppBar.chat({
    required String userName,
    String? userImage,
    String? subtitle,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
    VoidCallback? onUserTap,
  }) {
    return CustomAppBar(
      title: '',
      leading: IconButton(
        onPressed: onBackPressed,
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        tooltip: 'رجوع',
      ),
      actions: [
        Expanded(
          child: GestureDetector(
            onTap: onUserTap,
            child: Row(
              children: [
                Container(
                  width: 35,
                  height: 35,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: ClipOval(
                    child: userImage != null
                        ? Image.network(
                            userImage,
                            width: 35,
                            height: 35,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: AppColors.primary.withValues(alpha: 0.2),
                                child: const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 18,
                                ),
                              );
                            },
                          )
                        : Container(
                            color: AppColors.primary.withValues(alpha: 0.2),
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        userName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (subtitle != null)
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        ...?actions,
      ],
      backgroundColor: AppColors.primary,
      automaticallyImplyLeading: false,
    );
  }
}
