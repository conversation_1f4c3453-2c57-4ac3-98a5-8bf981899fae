import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/message_model.dart' hide ConversationModel;
import '../models/conversation_model.dart';
import '../models/notification_model.dart';
import 'notification_service.dart';

class MessageService {
  static final SupabaseClient _client = Supabase.instance.client;

  // Send a message
  static Future<Map<String, dynamic>> sendMessage({
    required String tripId,
    String? bookingId,
    required String senderId,
    required String receiverId,
    required String content,
    String messageType = 'text',
    Map<String, dynamic>? locationData,
  }) async {
    try {
      if (kDebugMode) {
        print('💬 Sending message from $senderId to $receiverId');
      }

      final messageData = {
        'trip_id': tripId,
        'booking_id': bookingId,
        'sender_id': senderId,
        'receiver_id': receiverId,
        'content': content,
        'message_type': messageType,
        'location_data': locationData,
        'is_read': false,
        'is_delivered': false,
      };

      final response = await _client
          .from('messages')
          .insert(messageData)
          .select('''
            *,
            sender:users!sender_id(*),
            receiver:users!receiver_id(*)
          ''')
          .single();

      final message = MessageModel.fromJson(response);

      // Update conversation last message
      await _updateConversationLastMessage(tripId, senderId, receiverId, message);

      // Send notification to receiver (unless it's a system message)
      if (messageType != 'system') {
        await _sendMessageNotification(message);
      }

      if (kDebugMode) {
        print('✅ Message sent successfully: ${message.id}');
      }

      return {
        'success': true,
        'message': message,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending message: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء إرسال الرسالة: $e',
      };
    }
  }

  // Get messages for a conversation
  static Future<List<MessageModel>> getConversationMessages({
    required String tripId,
    required String userId1,
    required String userId2,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final response = await _client
          .from('messages')
          .select('''
            *,
            sender:users!sender_id(*),
            receiver:users!receiver_id(*)
          ''')
          .eq('trip_id', tripId)
          .or('sender_id.eq.$userId1,sender_id.eq.$userId2')
          .or('receiver_id.eq.$userId1,receiver_id.eq.$userId2')
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return response.map<MessageModel>((json) => MessageModel.fromJson(json)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching messages: $e');
      }
      return [];
    }
  }

  // Mark messages as read
  static Future<bool> markMessagesAsRead({
    required String tripId,
    required String receiverId,
    required String senderId,
  }) async {
    try {
      await _client
          .from('messages')
          .update({'is_read': true})
          .eq('trip_id', tripId)
          .eq('receiver_id', receiverId)
          .eq('sender_id', senderId)
          .eq('is_read', false);

      // Update conversation unread count
      await _updateConversationUnreadCount(tripId, receiverId, senderId);

      if (kDebugMode) {
        print('✅ Messages marked as read');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking messages as read: $e');
      }
      return false;
    }
  }

  // Get or create conversation using database function
  static Future<ConversationModel?> getOrCreateConversation({
    required String tripId,
    String? bookingId,
    required String driverId,
    required String passengerId,
  }) async {
    try {
      if (kDebugMode) {
        print('💬 Getting/creating conversation: trip=$tripId, driver=$driverId, passenger=$passengerId');
      }

      // Use database function to safely get or create conversation
      final conversationId = await _client
          .rpc('get_or_create_conversation', params: {
            'p_trip_id': tripId,
            'p_booking_id': bookingId,
            'p_driver_id': driverId,
            'p_passenger_id': passengerId,
          });

      if (conversationId == null) {
        if (kDebugMode) {
          print('❌ Failed to get/create conversation');
        }
        return null;
      }

      // Fetch the complete conversation data
      final response = await _client
          .from('conversations')
          .select('''
            *,
            driver:users!driver_id(*),
            passenger:users!passenger_id(*)
          ''')
          .eq('id', conversationId)
          .single();

      if (kDebugMode) {
        print('✅ Conversation retrieved/created successfully');
      }

      return ConversationModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting/creating conversation: $e');
      }

      // Fallback to direct approach if function fails
      try {
        // Try to get existing conversation
        final existingResponse = await _client
            .from('conversations')
            .select('''
              *,
              driver:users!driver_id(*),
              passenger:users!passenger_id(*)
            ''')
            .eq('trip_id', tripId)
            .eq('passenger_id', passengerId)
            .maybeSingle();

        if (existingResponse != null) {
          return ConversationModel.fromJson(existingResponse);
        }

        // Create new conversation directly
        final conversationData = {
          'trip_id': tripId,
          'booking_id': bookingId,
          'driver_id': driverId,
          'passenger_id': passengerId,
          'unread_count_driver': 0,
          'unread_count_passenger': 0,
          'is_active': true,
        };

        final response = await _client
            .from('conversations')
            .insert(conversationData)
            .select('''
              *,
              driver:users!driver_id(*),
              passenger:users!passenger_id(*)
            ''')
            .single();

        return ConversationModel.fromJson(response);
      } catch (fallbackError) {
        if (kDebugMode) {
          print('❌ Fallback conversation creation also failed: $fallbackError');
        }
        return null;
      }
    }
  }

  // Get user conversations
  static Future<List<ConversationModel>> getUserConversations(String userId) async {
    try {
      final response = await _client
          .from('conversations')
          .select('''
            *,
            driver:users!driver_id(*),
            passenger:users!passenger_id(*),
            trip:trips(*)
          ''')
          .or('driver_id.eq.$userId,passenger_id.eq.$userId')
          .eq('is_active', true)
          .order('last_message_at', ascending: false);

      return response.map<ConversationModel>((json) => ConversationModel.fromJson(json)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching conversations: $e');
      }
      return [];
    }
  }

  // Send location message
  static Future<Map<String, dynamic>> sendLocationMessage({
    required String tripId,
    String? bookingId,
    required String senderId,
    required String receiverId,
    required double latitude,
    required double longitude,
    String? address,
  }) async {
    return await sendMessage(
      tripId: tripId,
      bookingId: bookingId,
      senderId: senderId,
      receiverId: receiverId,
      content: address ?? 'موقع مشترك',
      messageType: 'location',
      locationData: {
        'latitude': latitude,
        'longitude': longitude,
        'address': address,
      },
    );
  }

  // Send system message (for booking updates, etc.)
  static Future<Map<String, dynamic>> sendSystemMessage({
    required String tripId,
    String? bookingId,
    required String senderId,
    required String receiverId,
    required String content,
  }) async {
    return await sendMessage(
      tripId: tripId,
      bookingId: bookingId,
      senderId: senderId,
      receiverId: receiverId,
      content: content,
      messageType: 'system',
    );
  }

  // Subscribe to real-time messages for a conversation
  static RealtimeChannel subscribeToConversationMessages({
    required String tripId,
    required String userId,
    required Function(MessageModel) onMessageReceived,
  }) {
    if (kDebugMode) {
      print('💬 Subscribing to messages for trip: $tripId, user: $userId');
    }

    final channel = _client
        .channel('messages:$tripId:$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'messages',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'trip_id',
            value: tripId,
          ),
          callback: (payload) {
            try {
              final message = MessageModel.fromJson(payload.newRecord);
              
              // Only notify if the message is for this user
              if (message.receiverId == userId || message.senderId == userId) {
                onMessageReceived(message);
                
                if (kDebugMode) {
                  print('💬 New message received: ${message.content}');
                }
              }
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error processing message: $e');
              }
            }
          },
        )
        .subscribe();

    return channel;
  }

  // Unsubscribe from messages
  static Future<void> unsubscribeFromMessages(RealtimeChannel channel) async {
    try {
      await _client.removeChannel(channel);
      if (kDebugMode) {
        print('✅ Unsubscribed from messages');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error unsubscribing from messages: $e');
      }
    }
  }

  // Private helper methods
  static Future<void> _updateConversationLastMessage(
    String tripId,
    String senderId,
    String receiverId,
    MessageModel message,
  ) async {
    try {
      // Determine who is driver and who is passenger
      String? driverId, passengerId;
      
      // Get trip to determine driver
      final tripResponse = await _client
          .from('trips')
          .select('leader_id')
          .eq('id', tripId)
          .single();
      
      final tripLeaderId = tripResponse['leader_id'] as String;
      
      if (senderId == tripLeaderId) {
        driverId = senderId;
        passengerId = receiverId;
      } else {
        driverId = receiverId;
        passengerId = senderId;
      }

      // Update conversation
      await _client
          .from('conversations')
          .upsert({
            'trip_id': tripId,
            'driver_id': driverId,
            'passenger_id': passengerId,
            'last_message_id': message.id,
            'last_message_at': message.createdAt.toIso8601String(),
          });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating conversation: $e');
      }
    }
  }

  static Future<void> _updateConversationUnreadCount(
    String tripId,
    String receiverId,
    String senderId,
  ) async {
    try {
      // Get trip to determine who is driver/passenger
      final tripResponse = await _client
          .from('trips')
          .select('leader_id')
          .eq('id', tripId)
          .single();
      
      final tripLeaderId = tripResponse['leader_id'] as String;
      
      // Reset unread count for the receiver
      if (receiverId == tripLeaderId) {
        // Receiver is driver
        await _client
            .from('conversations')
            .update({'unread_count_driver': 0})
            .eq('trip_id', tripId)
            .eq('driver_id', receiverId);
      } else {
        // Receiver is passenger
        await _client
            .from('conversations')
            .update({'unread_count_passenger': 0})
            .eq('trip_id', tripId)
            .eq('passenger_id', receiverId);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating unread count: $e');
      }
    }
  }

  static Future<void> _sendMessageNotification(MessageModel message) async {
    try {
      final notification = NotificationModel.createNewMessage(
        userId: message.receiverId,
        tripId: message.tripId,
        bookingId: message.bookingId ?? '',
        senderName: message.sender?.fullName ?? 'مستخدم',
        messagePreview: message.content.length > 50
            ? '${message.content.substring(0, 50)}...'
            : message.content,
        relatedUserId: message.senderId,
      );

      await NotificationService.createNotification(
        userId: notification.userId,
        title: notification.title,
        message: notification.body,
        type: notification.notificationType,
        data: notification.data,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending message notification: $e');
      }
    }
  }

  /// Get total unread messages count for a user
  static Future<int> getUnreadMessagesCount(String userId) async {
    try {
      if (kDebugMode) {
        print('📊 Getting unread messages count for user: $userId');
      }

      // Get conversations where user is either driver or passenger
      final conversationsResponse = await _client
          .from('conversations')
          .select('unread_count_driver, unread_count_passenger, driver_id, passenger_id')
          .or('driver_id.eq.$userId,passenger_id.eq.$userId');

      int totalUnread = 0;

      for (final conversation in conversationsResponse) {
        if (conversation['driver_id'] == userId) {
          totalUnread += (conversation['unread_count_driver'] as int? ?? 0);
        } else if (conversation['passenger_id'] == userId) {
          totalUnread += (conversation['unread_count_passenger'] as int? ?? 0);
        }
      }

      if (kDebugMode) {
        print('✅ Total unread messages: $totalUnread');
      }

      return totalUnread;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting unread messages count: $e');
      }
      return 0;
    }
  }
}
