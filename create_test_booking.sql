-- Create test booking data for driver dashboard testing
-- This script creates a test passenger and booking for the current driver

-- First, create a test passenger user
INSERT INTO users (
    id,
    email,
    full_name,
    phone,
    role,
    city,
    created_at,
    updated_at
) VALUES (
    'test-passenger-001',
    '<EMAIL>',
    'فاطمة الزهراء',
    '+212612345678',
    'passenger',
    'الرباط',
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    phone = EXCLUDED.phone,
    updated_at = NOW();

-- Create a test booking for the driver's first trip
-- Replace 'DRIVER_ID_HERE' with the actual driver ID: 768f205b-6bc2-434c-856c-87c418e2d61d
-- Replace 'TRIP_ID_HERE' with the actual trip ID: 1db7f4db-b209-4365-bda6-c2b9e3a3fddc

INSERT INTO bookings (
    id,
    trip_id,
    passenger_id,
    driver_id,
    seats_booked,
    total_price,
    booking_type,
    status,
    message,
    special_requests,
    passenger_details,
    is_paid,
    created_at,
    updated_at
) VALUES (
    'test-booking-001',
    '1db7f4db-b209-4365-bda6-c2b9e3a3fddc',  -- Trip ID from logs
    'test-passenger-001',
    '768f205b-6bc2-434c-856c-87c418e2d61d',  -- Driver ID from logs
    1,
    220.0,
    'manual',
    'pending',
    'أتطلع للرحلة معكم، أرجو قبول طلب الحجز',
    'مقعد بجانب النافذة إذا أمكن',
    '{
        "passengers": [
            {
                "name": "فاطمة الزهراء",
                "phone": "+212612345678",
                "age": 28
            }
        ]
    }'::jsonb,
    false,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    status = 'pending',
    updated_at = NOW();

-- Create another test booking with different status
INSERT INTO bookings (
    id,
    trip_id,
    passenger_id,
    driver_id,
    seats_booked,
    total_price,
    booking_type,
    status,
    message,
    special_requests,
    passenger_details,
    is_paid,
    created_at,
    updated_at
) VALUES (
    'test-booking-002',
    '9456c729-c0c2-4897-93c8-cc86d0146c91',  -- Second trip ID from logs
    'test-passenger-001',
    '768f205b-6bc2-434c-856c-87c418e2d61d',  -- Driver ID from logs
    2,
    150.0,
    'manual',
    'pending',
    'هل يمكنني حجز مقعدين للرحلة؟',
    'نحتاج مساحة إضافية للأمتعة',
    '{
        "passengers": [
            {
                "name": "فاطمة الزهراء",
                "phone": "+212612345678",
                "age": 28
            },
            {
                "name": "أحمد محمد",
                "phone": "+212687654321",
                "age": 35
            }
        ]
    }'::jsonb,
    false,
    NOW() - INTERVAL '1 hour',
    NOW() - INTERVAL '1 hour'
) ON CONFLICT (id) DO UPDATE SET
    status = 'pending',
    updated_at = NOW();

-- Verify the bookings were created
SELECT 
    b.id,
    b.status,
    b.seats_booked,
    b.total_price,
    b.message,
    t.title as trip_title,
    t.from_city,
    t.to_city,
    u.full_name as passenger_name
FROM bookings b
JOIN trips t ON b.trip_id = t.id
JOIN users u ON b.passenger_id = u.id
WHERE b.driver_id = '768f205b-6bc2-434c-856c-87c418e2d61d'
ORDER BY b.created_at DESC;
