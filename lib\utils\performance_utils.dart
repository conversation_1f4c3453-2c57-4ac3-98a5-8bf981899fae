import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:universal_io/io.dart';

/// Utility class for performance optimization and monitoring
class PerformanceUtils {
  static final Map<String, DateTime> _timers = {};
  static final List<String> _performanceLogs = [];

  /// Start a performance timer
  static void startTimer(String name) {
    _timers[name] = DateTime.now();
    if (kDebugMode) {
      print('⏱️ Started timer: $name');
    }
  }

  /// End a performance timer and log the duration
  static Duration endTimer(String name) {
    final startTime = _timers[name];
    if (startTime == null) {
      if (kDebugMode) {
        print('❌ Timer not found: $name');
      }
      return Duration.zero;
    }

    final duration = DateTime.now().difference(startTime);
    _timers.remove(name);
    
    final logMessage = '$name: ${duration.inMilliseconds}ms';
    _performanceLogs.add(logMessage);
    
    if (kDebugMode) {
      print('⏱️ $logMessage');
    }
    
    return duration;
  }

  /// Get all performance logs
  static List<String> getPerformanceLogs() {
    return List.from(_performanceLogs);
  }

  /// Clear performance logs
  static void clearLogs() {
    _performanceLogs.clear();
  }

  /// Optimize image loading with caching
  static Widget optimizedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
            Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            const Icon(
              Icons.error,
              color: Colors.grey,
            );
      },
    );
  }

  /// Debounce function calls to improve performance
  static void debounce({
    required String key,
    required VoidCallback callback,
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, callback);
  }

  static final Map<String, Timer> _debounceTimers = {};

  /// Throttle function calls to limit execution frequency
  static void throttle({
    required String key,
    required VoidCallback callback,
    Duration interval = const Duration(milliseconds: 100),
  }) {
    if (_throttleTimers[key] == null || !_throttleTimers[key]!.isActive) {
      callback();
      _throttleTimers[key] = Timer(interval, () {
        _throttleTimers.remove(key);
      });
    }
  }

  static final Map<String, Timer> _throttleTimers = {};

  /// Optimize ListView with automatic disposal
  static Widget optimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    EdgeInsets? padding,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      cacheExtent: 250.0, // Optimize cache extent
    );
  }

  /// Memory-efficient image cache management
  static void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    if (kDebugMode) {
      print('🧹 Image cache cleared');
    }
  }

  /// Monitor memory usage
  static void logMemoryUsage(String context) {
    if (kDebugMode && !kIsWeb) {
      final info = ProcessInfo.currentRss;
      print('💾 Memory usage in $context: ${(info / 1024 / 1024).toStringAsFixed(2)} MB');
    }
  }

  /// Optimize Supabase queries with proper indexing hints
  static PostgrestFilterBuilder optimizeQuery(PostgrestFilterBuilder query) {
    // Add query optimizations
    return query.limit(50); // Reasonable default limit
  }

  /// Batch multiple operations for better performance
  static Future<List<T>> batchOperations<T>(
    List<Future<T> Function()> operations, {
    int batchSize = 5,
  }) async {
    final results = <T>[];
    
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize);
      final batchResults = await Future.wait(
        batch.map((op) => op()),
      );
      results.addAll(batchResults);
      
      // Small delay between batches to prevent overwhelming the system
      if (i + batchSize < operations.length) {
        await Future.delayed(const Duration(milliseconds: 10));
      }
    }
    
    return results;
  }

  /// Preload critical data
  static Future<void> preloadCriticalData() async {
    startTimer('preload_critical_data');
    
    try {
      // Preload user data, recent trips, etc.
      // This would be implemented based on your specific needs
      
      if (kDebugMode) {
        print('✅ Critical data preloaded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to preload critical data: $e');
      }
    } finally {
      endTimer('preload_critical_data');
    }
  }

  /// Optimize widget rebuilds with smart memoization
  static Widget memoizedWidget({
    required String key,
    required Widget Function() builder,
    List<Object?>? dependencies,
  }) {
    return _MemoizedWidget(
      key: ValueKey(key),
      builder: builder,
      dependencies: dependencies,
    );
  }

  /// Check if device has sufficient resources
  static bool hasGoodPerformance() {
    // This is a simplified check - in a real app you might want to
    // check device specs, available memory, etc.
    return !kDebugMode; // Assume release builds have better performance
  }

  /// Adaptive quality based on device performance
  static double getAdaptiveQuality() {
    return hasGoodPerformance() ? 1.0 : 0.8;
  }

  /// Clean up resources
  static void cleanup() {
    _debounceTimers.values.forEach((timer) => timer.cancel());
    _debounceTimers.clear();
    
    _throttleTimers.values.forEach((timer) => timer.cancel());
    _throttleTimers.clear();
    
    clearImageCache();
    clearLogs();
    
    if (kDebugMode) {
      print('🧹 Performance utils cleaned up');
    }
  }
}

/// Memoized widget for performance optimization
class _MemoizedWidget extends StatefulWidget {
  final Widget Function() builder;
  final List<Object?>? dependencies;

  const _MemoizedWidget({
    super.key,
    required this.builder,
    this.dependencies,
  });

  @override
  State<_MemoizedWidget> createState() => _MemoizedWidgetState();
}

class _MemoizedWidgetState extends State<_MemoizedWidget> {
  Widget? _cachedWidget;
  List<Object?>? _lastDependencies;

  @override
  Widget build(BuildContext context) {
    final currentDeps = widget.dependencies;
    
    if (_cachedWidget == null || 
        !listEquals(_lastDependencies, currentDeps)) {
      _cachedWidget = widget.builder();
      _lastDependencies = currentDeps?.toList();
    }
    
    return _cachedWidget!;
  }
}

/// Performance monitoring mixin
mixin PerformanceMonitorMixin<T extends StatefulWidget> on State<T> {
  late DateTime _buildStartTime;
  
  @override
  void initState() {
    super.initState();
    PerformanceUtils.startTimer('${T.toString()}_lifecycle');
  }
  
  @override
  Widget build(BuildContext context) {
    _buildStartTime = DateTime.now();
    final widget = buildWidget(context);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final buildTime = DateTime.now().difference(_buildStartTime);
      if (kDebugMode && buildTime.inMilliseconds > 16) {
        print('⚠️ Slow build detected in ${T.toString()}: ${buildTime.inMilliseconds}ms');
      }
    });
    
    return widget;
  }
  
  @override
  void dispose() {
    PerformanceUtils.endTimer('${T.toString()}_lifecycle');
    super.dispose();
  }
  
  /// Override this instead of build()
  Widget buildWidget(BuildContext context);
}

/// Timer utility for Dart
class CustomTimer {
  final Duration duration;
  final VoidCallback callback;
  bool _isActive = true;

  CustomTimer(this.duration, this.callback) {
    Future.delayed(duration).then((_) {
      if (_isActive) {
        callback();
      }
    });
  }

  bool get isActive => _isActive;

  void cancel() {
    _isActive = false;
  }
}
