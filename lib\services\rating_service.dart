import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../models/trip_model.dart';
import '../models/booking_model.dart';

class RatingModel {
  final String id;
  final String tripId;
  final String bookingId;
  final String raterId; // Who gave the rating
  final String ratedUserId; // Who received the rating
  final double rating;
  final String? comment;
  final String ratingType; // 'driver_to_passenger', 'passenger_to_driver'
  final DateTime createdAt;

  RatingModel({
    required this.id,
    required this.tripId,
    required this.bookingId,
    required this.raterId,
    required this.ratedUserId,
    required this.rating,
    this.comment,
    required this.ratingType,
    required this.createdAt,
  });

  factory RatingModel.fromJson(Map<String, dynamic> json) {
    return RatingModel(
      id: json['id'] as String,
      tripId: json['trip_id'] as String,
      bookingId: json['booking_id'] as String,
      raterId: json['rater_id'] as String,
      ratedUserId: json['rated_user_id'] as String,
      rating: (json['rating'] as num).toDouble(),
      comment: json['comment'] as String?,
      ratingType: json['rating_type'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'booking_id': bookingId,
      'rater_id': raterId,
      'rated_user_id': ratedUserId,
      'rating': rating,
      'comment': comment,
      'rating_type': ratingType,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class RatingService {
  static final SupabaseClient _client = Supabase.instance.client;

  // Submit a rating
  static Future<Map<String, dynamic>> submitRating({
    required String tripId,
    required String bookingId,
    required String raterId,
    required String ratedUserId,
    required double rating,
    String? comment,
    required String ratingType,
  }) async {
    try {
      if (kDebugMode) {
        print('⭐ Submitting rating: $rating stars for user $ratedUserId');
      }

      // Check if rating already exists
      final existingRating = await _client
          .from('ratings')
          .select('id')
          .eq('trip_id', tripId)
          .eq('booking_id', bookingId)
          .eq('rater_id', raterId)
          .eq('rating_type', ratingType)
          .maybeSingle();

      if (existingRating != null) {
        return {
          'success': false,
          'error': 'لقد قمت بتقييم هذه الرحلة مسبقاً',
        };
      }

      // Insert the rating
      final ratingData = {
        'trip_id': tripId,
        'booking_id': bookingId,
        'rater_id': raterId,
        'rated_user_id': ratedUserId,
        'rating': rating,
        'comment': comment,
        'rating_type': ratingType,
      };

      final response = await _client
          .from('ratings')
          .insert(ratingData)
          .select()
          .single();

      // Update user's average rating
      await _updateUserRating(ratedUserId);

      if (kDebugMode) {
        print('✅ Rating submitted successfully');
      }

      return {
        'success': true,
        'rating': RatingModel.fromJson(response),
        'message': 'تم إرسال التقييم بنجاح',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error submitting rating: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء إرسال التقييم: $e',
      };
    }
  }

  // Update user's average rating
  static Future<void> _updateUserRating(String userId) async {
    try {
      // Calculate average rating
      final ratingsResponse = await _client
          .from('ratings')
          .select('rating')
          .eq('rated_user_id', userId);

      if (ratingsResponse.isEmpty) return;

      final ratings = ratingsResponse.map((r) => (r['rating'] as num).toDouble()).toList();
      final averageRating = ratings.reduce((a, b) => a + b) / ratings.length;
      final totalRatings = ratings.length;

      // Update user's rating
      await _client
          .from('users')
          .update({
            'rating': averageRating,
            'total_ratings': totalRatings,
          })
          .eq('id', userId);

      if (kDebugMode) {
        print('✅ Updated user rating: $averageRating ($totalRatings ratings)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating user rating: $e');
      }
    }
  }

  // Get ratings for a user
  static Future<List<RatingModel>> getUserRatings(String userId, {int limit = 20}) async {
    try {
      final response = await _client
          .from('ratings')
          .select('''
            *,
            rater:users!rater_id(*),
            trip:trips(*)
          ''')
          .eq('rated_user_id', userId)
          .order('created_at', ascending: false)
          .limit(limit);

      return response.map<RatingModel>((json) => RatingModel.fromJson(json)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching user ratings: $e');
      }
      return [];
    }
  }

  // Get rating for a specific trip and user
  static Future<RatingModel?> getTripRating({
    required String tripId,
    required String bookingId,
    required String raterId,
    required String ratingType,
  }) async {
    try {
      final response = await _client
          .from('ratings')
          .select('*')
          .eq('trip_id', tripId)
          .eq('booking_id', bookingId)
          .eq('rater_id', raterId)
          .eq('rating_type', ratingType)
          .maybeSingle();

      if (response != null) {
        return RatingModel.fromJson(response);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching trip rating: $e');
      }
      return null;
    }
  }

  // Check if user can rate a trip
  static Future<Map<String, dynamic>> canRateTrip({
    required String tripId,
    required String bookingId,
    required String userId,
  }) async {
    try {
      // Check if booking is completed
      final booking = await _client
          .from('bookings')
          .select('status, passenger_id, driver_id')
          .eq('id', bookingId)
          .eq('trip_id', tripId)
          .single();

      if (booking['status'] != 'completed') {
        return {
          'canRate': false,
          'reason': 'يمكن التقييم فقط بعد إكمال الرحلة',
        };
      }

      final isPassenger = booking['passenger_id'] == userId;
      final isDriver = booking['driver_id'] == userId;

      if (!isPassenger && !isDriver) {
        return {
          'canRate': false,
          'reason': 'غير مصرح لك بتقييم هذه الرحلة',
        };
      }

      // Determine rating type and target user
      String ratingType;
      String targetUserId;

      if (isPassenger) {
        ratingType = 'passenger_to_driver';
        targetUserId = booking['driver_id'];
      } else {
        ratingType = 'driver_to_passenger';
        targetUserId = booking['passenger_id'];
      }

      // Check if already rated
      final existingRating = await getTripRating(
        tripId: tripId,
        bookingId: bookingId,
        raterId: userId,
        ratingType: ratingType,
      );

      if (existingRating != null) {
        return {
          'canRate': false,
          'reason': 'لقد قمت بتقييم هذه الرحلة مسبقاً',
          'existingRating': existingRating,
        };
      }

      return {
        'canRate': true,
        'ratingType': ratingType,
        'targetUserId': targetUserId,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking rating eligibility: $e');
      }
      return {
        'canRate': false,
        'reason': 'حدث خطأ أثناء التحقق من إمكانية التقييم',
      };
    }
  }

  // Get trip statistics for a user
  static Future<Map<String, dynamic>> getUserTripStats(String userId) async {
    try {
      // Get trips as driver
      final driverTripsResponse = await _client
          .from('trips')
          .select('id, status')
          .eq('leader_id', userId);

      // Get bookings as passenger
      final passengerBookingsResponse = await _client
          .from('bookings')
          .select('id, status')
          .eq('passenger_id', userId);

      // Get ratings received
      final ratingsResponse = await _client
          .from('ratings')
          .select('rating')
          .eq('rated_user_id', userId);

      final driverTrips = driverTripsResponse.length;
      final completedDriverTrips = driverTripsResponse.where((t) => t['status'] == 'completed').length;
      
      final passengerTrips = passengerBookingsResponse.length;
      final completedPassengerTrips = passengerBookingsResponse.where((b) => b['status'] == 'completed').length;

      final totalRatings = ratingsResponse.length;
      final averageRating = totalRatings > 0 
          ? ratingsResponse.map((r) => (r['rating'] as num).toDouble()).reduce((a, b) => a + b) / totalRatings
          : 0.0;

      return {
        'driverTrips': driverTrips,
        'completedDriverTrips': completedDriverTrips,
        'passengerTrips': passengerTrips,
        'completedPassengerTrips': completedPassengerTrips,
        'totalTrips': driverTrips + passengerTrips,
        'completedTrips': completedDriverTrips + completedPassengerTrips,
        'totalRatings': totalRatings,
        'averageRating': averageRating,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching user trip stats: $e');
      }
      return {
        'driverTrips': 0,
        'completedDriverTrips': 0,
        'passengerTrips': 0,
        'completedPassengerTrips': 0,
        'totalTrips': 0,
        'completedTrips': 0,
        'totalRatings': 0,
        'averageRating': 0.0,
      };
    }
  }
}
