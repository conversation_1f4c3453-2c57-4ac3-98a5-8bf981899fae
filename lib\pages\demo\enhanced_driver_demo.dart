import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../models/user_model.dart';
import '../profile/driver_activation_page.dart';

class EnhancedDriverDemo extends StatelessWidget {
  const EnhancedDriverDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Driver Mode Demo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary.withValues(alpha: 0.05),
              AppColors.secondary.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Safe<PERSON>rea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.drive_eta,
                        size: 48,
                        color: AppColors.primary,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Enhanced Driver Mode Activation',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Test the improved driver activation flow with enhanced image picker and auto-population',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Features list
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Enhanced Features:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        icon: Icons.photo_camera,
                        title: 'Dual-Source Image Picker',
                        description: 'Camera and gallery options with enhanced UI',
                      ),
                      _buildFeatureItem(
                        icon: Icons.auto_awesome,
                        title: 'Auto-Population',
                        description: 'Profile image and name automatically loaded',
                      ),
                      _buildFeatureItem(
                        icon: Icons.timeline,
                        title: '25% Progress Indicator',
                        description: 'Brand-aligned progress tracking',
                      ),
                      _buildFeatureItem(
                        icon: Icons.check_circle,
                        title: 'Smart Validation',
                        description: 'Next button activates when requirements are met',
                      ),
                    ],
                  ),
                ),
                
                const Spacer(),
                
                // Demo buttons
                Column(
                  children: [
                    // Test with existing user
                    ElevatedButton(
                      onPressed: () => _testWithExistingUser(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.person),
                          SizedBox(width: 8),
                          Text(
                            'Test with Existing User Profile',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Test with new user
                    OutlinedButton(
                      onPressed: () => _testWithNewUser(context),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppColors.primary, width: 2),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.person_add, color: AppColors.primary),
                          const SizedBox(width: 8),
                          Text(
                            'Test with New User Profile',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AppColors.primary, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _testWithExistingUser(BuildContext context) {
    // Create a mock user with existing profile
    final mockUser = UserModel(
      id: 'demo-user-existing',
      phone: '+212612345678',
      fullName: 'أحمد محمد السائق',
      role: 'traveler',
      profileImageUrl: 'https://via.placeholder.com/150/1565C0/FFFFFF?text=أحمد',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Set the mock user in the auth provider
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    authProvider.setCurrentUser(mockUser);

    // Navigate to driver activation
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DriverActivationPage(),
      ),
    );
  }

  void _testWithNewUser(BuildContext context) {
    // Create a mock user without profile image
    final mockUser = UserModel(
      id: 'demo-user-new',
      phone: '+212687654321',
      fullName: 'فاطمة الزهراء',
      role: 'traveler',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Set the mock user in the auth provider
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    authProvider.setCurrentUser(mockUser);

    // Navigate to driver activation
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DriverActivationPage(),
      ),
    );
  }
}
