import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

/// Service for handling file uploads, downloads, and management in Supabase Storage.
class StorageService {
  static final _supabase = Supabase.instance.client;

  // Bucket names
  static const String _profileImagesBucket = 'profile-images';
  static const String _driverLicensesBucket = 'driver-licenses';
  static const String _tripGalleryBucket = 'trip-gallery';

  /// Generic method to upload a file to Supabase Storage.
  ///
  /// Can handle both public and private uploads.
  /// For public files, it returns a public URL.
  /// For private files, it returns the file path for later use with signed URLs.
  static Future<String?> uploadImage({
    required PlatformFile imageFile,
    required String bucket,
    String? folder,
    String? customFileName,
    String? userId,
    bool isPrivate = false,
  }) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('Upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own files when userId is provided
      if (userId != null && currentUser.id != userId) {
        if (kDebugMode) {
          print('Upload failed: User ID mismatch');
        }
        return null;
      }

      // Generate filename
      final uuid = const Uuid();
      final fileExtension = imageFile.name.split('.').last.toLowerCase();
      final fileName = customFileName ?? '${uuid.v4()}.$fileExtension';
      final fullPath = folder != null ? '$folder/$fileName' : fileName;

      // Read image bytes (works for both web and mobile)
      final Uint8List imageBytes = imageFile.bytes ?? Uint8List(0);

      // Determine content type
      String contentType;
      switch (fileExtension) {
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
        case 'png':
          contentType = 'image/png';
          break;
        case 'webp':
          contentType = 'image/webp';
          break;
        default:
          contentType = 'image/jpeg';
      }

      // Prepare upload options
      final uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
        contentType: contentType,
      );

      if (kDebugMode) {
        print('Uploading to bucket: $bucket, path: $fullPath');
      }

      // Upload to Supabase Storage
      await _supabase.storage
          .from(bucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      // Return the file path for private files, public URL for public files
      if (isPrivate) {
        if (kDebugMode) {
          print('Private file uploaded successfully: $fullPath');
        }
        return fullPath; // Return path for signed URL generation
      } else {
        // Get public URL for public files
        final publicUrl =
            _supabase.storage.from(bucket).getPublicUrl(fullPath);

        if (kDebugMode) {
          print('Public file uploaded successfully: $publicUrl');
        }
        return publicUrl;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
        if (e is StorageException) {
          print('Status code: ${e.statusCode}');
          print('Message: ${e.message}');
        }
      }
      return null;
    }
  }

  /// Uploads a user's profile image to the public `profile-images` bucket.
  static Future<String?> uploadUserProfileImage({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      bucket: _profileImagesBucket,
      folder: 'users',
      customFileName: 'profile_$userId.jpeg', // Force .jpeg for consistency
      userId: userId,
      isPrivate: false,
    );
  }

  /// Uploads a profile image (alias for uploadUserProfileImage for compatibility)
  static Future<String?> uploadProfileImage({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    return await uploadUserProfileImage(
      imageFile: imageFile,
      userId: userId,
    );
  }

  /// Uploads a driver's license image to the private `driver-licenses` bucket.
  static Future<String?> uploadDriverLicense({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('License upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own license
      if (currentUser.id != userId) {
        if (kDebugMode) {
          print('License upload failed: User ID mismatch');
        }
        return null;
      }

      // Force .jpeg extension for consistency
      final fileName = 'license_$userId.jpeg';
      final fullPath = 'licenses/$fileName';

      if (kDebugMode) {
        print('License upload path: $fullPath');
        print('Bucket: $_driverLicensesBucket (private)');
        print('User ID: ${currentUser.id}');
      }

      // Read image bytes
      final Uint8List imageBytes = imageFile.bytes ?? Uint8List(0);

      if (kDebugMode) {
        print('File size: ${imageBytes.length} bytes');
      }

      // Upload the file with .jpeg format
      final uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
        contentType: 'image/jpeg',
      );

      if (kDebugMode) {
        print('Starting upload to Supabase...');
      }

      await _supabase.storage
          .from(_driverLicensesBucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);


      if (kDebugMode) {
        print('File uploaded successfully!');
        print('Path: $fullPath');
        print('Driver license upload completed successfully');
      }

      // Return success status - license uploaded to private bucket
      return fullPath;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading driver license: $e');
      }
      // Return null for any upload error
      return null;
    }
  }

  /// Uploads a car image to the public `profile-images` bucket under the `vehicles` folder.
  static Future<String?> uploadCarImage({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      bucket: _profileImagesBucket, // Cars are public in the profile bucket
      folder: 'vehicles',
      customFileName: 'vehicle_$userId.${imageFile.name.split('.').last}',
      userId: userId,
      isPrivate: false,
    );
  }

  /// Uploads a trip gallery image to the public `trip-gallery` bucket.
  static Future<String?> uploadTripImage({
    required PlatformFile imageFile,
    required String tripId,
    int? imageIndex,
  }) async {
    final fileName = imageIndex != null
        ? 'trip_${tripId}_$imageIndex.${imageFile.name.split('.').last}'
        : null;

    return await uploadImage(
      imageFile: imageFile,
      bucket: _tripGalleryBucket,
      folder: 'trips',
      customFileName: fileName,
    );
  }

  /// Deletes an image from a specified storage bucket.
  static Future<bool> deleteImage({
    required String bucket,
    required String filePath,
  }) async {
    try {
      await _supabase.storage.from(bucket).remove([filePath]);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting image: $e');
      }
      return false;
    }
  }

  /// Extracts the file path from a Supabase public URL.
  static String? getFilePathFromUrl(String publicUrl, String bucket) {
    try {
      final uri = Uri.parse(publicUrl);
      final pathSegments = uri.pathSegments;
      final bucketIndex = pathSegments.indexOf(bucket);

      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        return pathSegments.sublist(bucketIndex + 1).join('/');
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing URL: $e');
      }
      return null;
    }
  }

  /// Gets the public URL for a user's profile image.
  ///
  /// If a `storedUrl` is provided, it is returned directly.
  /// Otherwise, a URL is generated based on the user's ID.
  static String? getProfileImageUrl(String userId, {String? storedUrl}) {
    try {
      // If we have a stored URL, use it
      if (storedUrl != null && storedUrl.isNotEmpty) {
        return storedUrl;
      }

      // Generate public URL for profile image using .jpeg extension exactly
      final filePath = 'users/profile_$userId.jpeg';
      final url =
          _supabase.storage.from(_profileImagesBucket).getPublicUrl(filePath);

      if (kDebugMode) {
        print('Generated profile URL: $url');
      }
      return url;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting profile image URL: $e');
      }
      return null;
    }
  }

  /// Generates a public URL for a user's profile image.
  static String getProfileImagePublicUrl(String userId) {
    final filePath = 'users/profile_$userId.jpeg';
    return _supabase.storage.from(_profileImagesBucket).getPublicUrl(filePath);
  }

  /// Gets the profile image URL with an optional delay.
  ///
  /// Useful for ensuring the image is available after a recent upload.
  static Future<String?> getProfileImageUrlWithDelay(
    String userId, {
    String? storedUrl,
    Duration delay = const Duration(seconds: 1),
  }) async {
    // If we have a stored URL, use it immediately
    if (storedUrl != null && storedUrl.isNotEmpty) {
      return storedUrl;
    }

    // Wait for the specified delay to ensure public availability
    await Future.delayed(delay);

    // Generate the URL
    return getProfileImageUrl(userId, storedUrl: storedUrl);
  }

  /// Gets the public URL for a user's car image.
  static String? getCarImageUrl(String userId) {
    try {
      // Try different file extensions in profile-images/vehicles/
      final extensions = ['jpg', 'jpeg', 'png', 'webp'];
      for (final ext in extensions) {
        final filePath = 'vehicles/vehicle_$userId.$ext';
        final url =
            _supabase.storage.from(_profileImagesBucket).getPublicUrl(filePath);

        // A simple check to see if the URL is likely valid
        if (url.isNotEmpty && !url.endsWith(filePath)) {
          return url;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting car image URL: $e');
      }
      return null;
    }
  }

  /// Checks if a file exists in a specified bucket.
  static Future<bool> imageExists({
    required String bucket,
    required String filePath,
  }) async {
    try {
      final pathParts = filePath.split('/');
      final folder = pathParts.length > 1 ? pathParts.first : '';
      final fileName = pathParts.last;

      final response = await _supabase.storage.from(bucket).list(path: folder);

      return response.any((file) => file.name == fileName);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking image existence: $e');
      }
      return false;
    }
  }

  /// Performs a health check on the storage service by listing files in all buckets.
  static Future<bool> checkStorageHealth() async {
    try {
      // Try to list files in each bucket to verify access
      await _supabase.storage.from(_driverLicensesBucket).list();
      await _supabase.storage.from(_profileImagesBucket).list();
      await _supabase.storage.from(_tripGalleryBucket).list();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Storage health check failed: $e');
      }
      return false;
    }
  }
}