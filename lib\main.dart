// ignore_for_file: duplicate_import

import "package:flutter/material.dart";
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'constants/app_theme.dart';
import 'pages/splash_page.dart';
import 'pages/simple_splash_page.dart';
import 'pages/profile/profile_demo_page.dart';
import 'pages/demo/profile_upload_demo.dart';
import 'providers/auth_provider.dart';
import 'providers/trip_provider.dart';
import 'providers/user_provider.dart';
import 'providers/notification_provider.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the app with error handling
  await _initializeApp();

  runApp(const SafarniApp());
}

Future<void> _initializeApp() async {
  try {
    // Initialize timeago Arabic locale
    timeago.setLocaleMessages('ar', timeago.ArMessages());

    // Initialize Supabase with web-compatible configuration
    if (kIsWeb) {
      // Web-specific Supabase initialization
      await Supabase.initialize(
        url: 'https://pmykpbrecfoxgtahetgb.supabase.co',
        anonKey:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.jxkxOW46KnVWUGR8TClVWMlOevRcwrUSfiZfQ6qMDgE',
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
        ),
      );
    } else {
      // Mobile-specific Supabase initialization
      await Supabase.initialize(
        url: 'https://pmykpbrecfoxgtahetgb.supabase.co',
        anonKey:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.jxkxOW46KnVWUGR8TClVWMlOevRcwrUSfiZfQ6qMDgE',
      );

      // Set preferred orientations only for mobile
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }
  } catch (e) {
    if (kDebugMode) {
      print('Initialization error: $e');
    }
    // Continue anyway to avoid blocking the app
  }
}

class SafarniApp extends StatelessWidget {
  const SafarniApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Create providers lazily to avoid initialization issues
        ChangeNotifierProvider(create: (_) => AuthProvider(), lazy: true),
        ChangeNotifierProvider(create: (_) => TripProvider(), lazy: true),
        ChangeNotifierProvider(create: (_) => UserProvider(), lazy: true),
        ChangeNotifierProvider(create: (_) => NotificationProvider(), lazy: true),
      ],
      child: MaterialApp(
        title: 'سفرني',
        debugShowCheckedModeBanner: false,

        // الدعم اللغوي
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('ar'), Locale('en')],
        locale: const Locale('ar'),

        // الثيم - Use fallback theme if AppTheme fails
        theme: _buildSafeTheme(),

        // الصفحة الرئيسية - Use simple splash for web
        home: kIsWeb ? const SimpleSplashPage() : const SplashPage(),

        // Routes for navigation
        routes: {
          '/profile-demo': (context) => const ProfileDemoPage(),
          '/profile-upload-demo': (context) => const ProfileUploadDemo(),
        },

        // Error handling
        builder: (context, child) {
          // Add error handling wrapper
          ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
            return Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 60,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'حدث خطأ في التطبيق',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    if (kDebugMode)
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          errorDetails.toString(),
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ],
                ),
              ),
            );
          };

          return Directionality(
            textDirection: TextDirection.rtl,
            child: child!,
          );
        },
      ),
    );
  }

  ThemeData _buildSafeTheme() {
    try {
      // Use a web-safe theme for better compatibility
      if (kIsWeb) {
        return ThemeData(
          primarySwatch: Colors.blue,
          primaryColor: const Color(0xFF1565C0),
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF1565C0),
            brightness: Brightness.light,
          ),
          appBarTheme: const AppBarTheme(
            backgroundColor: Color(0xFF1565C0),
            foregroundColor: Colors.white,
            elevation: 0,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1565C0),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          useMaterial3: true,
        );
      }
      return AppTheme.lightTheme;
    } catch (e) {
      print('Theme error: $e');
      // Fallback theme
      return ThemeData(
        primarySwatch: Colors.blue,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        useMaterial3: true,
      );
    }
  }
}
