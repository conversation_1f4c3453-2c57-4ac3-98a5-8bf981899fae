-- =====================================================
-- SUPABASE FIXES FOR RIDE-SHARING APP
-- Fix foreign key issues and schema inconsistencies
-- =====================================================

-- Enhanced conversations table with proper relationships and unread counts
CREATE TABLE IF NOT EXISTS public.conversations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    driver_id UUID NOT NULL,
    passenger_id UUID NOT NULL,
    trip_id UUID,
    booking_id UUID,
    last_message TEXT,
    last_message_at TIMESTAMP WITH TIME ZONE,
    unread_count_driver INTEGER DEFAULT 0,
    unread_count_passenger INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Foreign key constraints
    CONSTRAINT conversations_driver_id_fkey
        FOREIGN KEY (driver_id) REFERENCES public.users(id) ON DELETE CASCADE,
    CONSTRAINT conversations_passenger_id_fkey
        FOREIGN KEY (passenger_id) REFERENCES public.users(id) ON DELETE CASCADE,
    CONSTRAINT conversations_trip_id_fkey
        FOREIGN KEY (trip_id) REFERENCES public.trips(id) ON DELETE SET NULL,
    CONSTRAINT conversations_booking_id_fkey
        FOREIGN KEY (booking_id) REFERENCES public.bookings(id) ON DELETE SET NULL,

    -- Unique constraint to prevent duplicate conversations
    CONSTRAINT conversations_unique_pair
        UNIQUE (driver_id, passenger_id, trip_id)
);

-- Add missing columns if they don't exist
ALTER TABLE public.conversations
ADD COLUMN IF NOT EXISTS booking_id UUID,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS unread_count_driver INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS unread_count_passenger INTEGER DEFAULT 0;

-- Add unread count columns if they don't exist
ALTER TABLE public.conversations
ADD COLUMN IF NOT EXISTS unread_count_driver INTEGER DEFAULT 0;

ALTER TABLE public.conversations
ADD COLUMN IF NOT EXISTS unread_count_passenger INTEGER DEFAULT 0;

-- 1. Fix conversations table foreign key references
-- The error occurs because conversations.driver_id references users.id
-- but the foreign key constraint might be missing or incorrect

-- First, let's ensure the foreign key constraints exist
ALTER TABLE public.conversations 
DROP CONSTRAINT IF EXISTS conversations_driver_id_fkey;

ALTER TABLE public.conversations 
DROP CONSTRAINT IF EXISTS conversations_passenger_id_fkey;

-- Add proper foreign key constraints
ALTER TABLE public.conversations 
ADD CONSTRAINT conversations_driver_id_fkey 
FOREIGN KEY (driver_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.conversations 
ADD CONSTRAINT conversations_passenger_id_fkey 
FOREIGN KEY (passenger_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 2. Fix bookings table schema inconsistencies
-- Ensure bookings table has consistent column names
ALTER TABLE public.bookings 
DROP CONSTRAINT IF EXISTS bookings_passenger_id_fkey;

ALTER TABLE public.bookings 
DROP CONSTRAINT IF EXISTS bookings_driver_id_fkey;

-- Rename traveler_id to passenger_id if it exists (for consistency)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'bookings' AND column_name = 'traveler_id') THEN
        ALTER TABLE public.bookings RENAME COLUMN traveler_id TO passenger_id;
    END IF;
END $$;

-- Add proper foreign key constraints for bookings
ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_passenger_id_fkey 
FOREIGN KEY (passenger_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_driver_id_fkey 
FOREIGN KEY (driver_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 3. Ensure trips table has proper driver_id column and constraint
-- Add driver_id column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'trips' AND column_name = 'driver_id') THEN
        ALTER TABLE public.trips ADD COLUMN driver_id UUID;
    END IF;
END $$;

-- Update existing trips to set driver_id = leader_id where driver_id is null
UPDATE public.trips 
SET driver_id = leader_id 
WHERE driver_id IS NULL;

-- Add foreign key constraint for trips.driver_id
ALTER TABLE public.trips 
DROP CONSTRAINT IF EXISTS trips_driver_id_fkey;

ALTER TABLE public.trips 
ADD CONSTRAINT trips_driver_id_fkey 
FOREIGN KEY (driver_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 4. Fix messages table foreign key constraints
ALTER TABLE public.messages 
DROP CONSTRAINT IF EXISTS messages_sender_id_fkey;

ALTER TABLE public.messages 
DROP CONSTRAINT IF EXISTS messages_receiver_id_fkey;

ALTER TABLE public.messages 
ADD CONSTRAINT messages_sender_id_fkey 
FOREIGN KEY (sender_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.messages 
ADD CONSTRAINT messages_receiver_id_fkey 
FOREIGN KEY (receiver_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 5. Create improved function for conversation creation
CREATE OR REPLACE FUNCTION get_or_create_conversation(
    p_trip_id UUID,
    p_booking_id UUID DEFAULT NULL,
    p_driver_id UUID,
    p_passenger_id UUID
) RETURNS UUID AS $$
DECLARE
    conversation_id UUID;
BEGIN
    -- Try to find existing conversation
    SELECT id INTO conversation_id
    FROM public.conversations
    WHERE trip_id = p_trip_id
    AND passenger_id = p_passenger_id
    LIMIT 1;

    -- If not found, create new conversation
    IF conversation_id IS NULL THEN
        INSERT INTO public.conversations (
            trip_id,
            booking_id,
            driver_id,
            passenger_id,
            unread_count_driver,
            unread_count_passenger,
            is_active
        ) VALUES (
            p_trip_id,
            p_booking_id,
            p_driver_id,
            p_passenger_id,
            0,
            0,
            true
        ) RETURNING id INTO conversation_id;
    END IF;
    
    RETURN conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5.1. Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    related_user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    trip_id UUID REFERENCES public.trips(id) ON DELETE SET NULL,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
    type TEXT NOT NULL CHECK (type IN ('booking_request', 'booking_accepted', 'booking_rejected', 'message', 'trip_update', 'payment')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for notifications
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(user_id, is_read);

-- 6. Create function to get driver bookings with proper filtering
CREATE OR REPLACE FUNCTION get_driver_bookings(
    p_driver_id UUID,
    p_status TEXT DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    trip_id UUID,
    passenger_id UUID,
    driver_id UUID,
    seats_booked INTEGER,
    total_price DECIMAL,
    status TEXT,
    booking_type TEXT,
    message TEXT,
    special_requests TEXT,
    passenger_details JSONB,
    has_luggage BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    trip_data JSONB,
    passenger_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.id,
        b.trip_id,
        b.passenger_id,
        b.driver_id,
        b.seats_booked,
        b.total_price,
        b.status,
        b.booking_type,
        b.message,
        b.special_requests,
        b.passenger_details,
        b.has_luggage,
        b.created_at,
        b.confirmed_at,
        to_jsonb(t.*) as trip_data,
        to_jsonb(u.*) as passenger_data
    FROM public.bookings b
    JOIN public.trips t ON b.trip_id = t.id
    JOIN public.users u ON b.passenger_id = u.id
    WHERE b.driver_id = p_driver_id
    AND (p_status IS NULL OR b.status = p_status)
    ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create function to get passenger bookings
CREATE OR REPLACE FUNCTION get_passenger_bookings(
    p_passenger_id UUID,
    p_status TEXT DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    trip_id UUID,
    passenger_id UUID,
    driver_id UUID,
    seats_booked INTEGER,
    total_price DECIMAL,
    status TEXT,
    booking_type TEXT,
    message TEXT,
    special_requests TEXT,
    passenger_details JSONB,
    has_luggage BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    trip_data JSONB,
    driver_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        b.id,
        b.trip_id,
        b.passenger_id,
        b.driver_id,
        b.seats_booked,
        b.total_price,
        b.status,
        b.booking_type,
        b.message,
        b.special_requests,
        b.passenger_details,
        COALESCE((b.passenger_details->>'has_luggage')::boolean, false) as has_luggage,
        b.created_at,
        b.confirmed_at,
        to_jsonb(t.*) as trip_data,
        to_jsonb(u.*) as driver_data
    FROM public.bookings b
    JOIN public.trips t ON b.trip_id = t.id
    JOIN public.users u ON b.driver_id = u.id
    WHERE b.passenger_id = p_passenger_id
    AND (p_status IS NULL OR b.status = p_status)
    ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7.1. Update RLS policies for notifications
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can insert their own notifications" ON public.notifications;
CREATE POLICY "Users can insert their own notifications" ON public.notifications
    FOR INSERT WITH CHECK (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (user_id = auth.uid());

-- Enable RLS on notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- 8. Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_or_create_conversation TO authenticated;
GRANT EXECUTE ON FUNCTION get_driver_bookings TO authenticated;
GRANT EXECUTE ON FUNCTION get_passenger_bookings TO authenticated;

-- Grant permissions on tables
GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.conversations TO authenticated;

-- 9. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_driver_status ON public.bookings(driver_id, status);
CREATE INDEX IF NOT EXISTS idx_bookings_passenger_status ON public.bookings(passenger_id, status);
CREATE INDEX IF NOT EXISTS idx_conversations_trip_passenger ON public.conversations(trip_id, passenger_id);

-- 10. Update RLS policies to work with new schema
DROP POLICY IF EXISTS "Users can view their own conversations" ON public.conversations;
CREATE POLICY "Users can view their own conversations" ON public.conversations
    FOR SELECT USING (
        driver_id = auth.uid() OR 
        passenger_id = auth.uid()
    );

DROP POLICY IF EXISTS "Users can view their own bookings" ON public.bookings;
CREATE POLICY "Users can view their own bookings" ON public.bookings
    FOR SELECT USING (
        passenger_id = auth.uid() OR 
        driver_id = auth.uid()
    );

-- Success message
SELECT 'Supabase schema fixes applied successfully!' as result;
