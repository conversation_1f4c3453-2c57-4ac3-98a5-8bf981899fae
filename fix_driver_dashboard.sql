-- =====================================================
-- CRITICAL FIX FOR DRIVER DASHBOARD BOOKING DISPLAY
-- This script ensures all necessary functions and schema exist
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. ENSURE BOOKINGS TABLE HAS CORRECT STRUCTURE
-- =====================================================

-- Add any missing columns to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS booking_type TEXT DEFAULT 'manual',
ADD COLUMN IF NOT EXISTS special_requests TEXT,
ADD COLUMN IF NOT EXISTS passenger_details JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS confirmed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Ensure proper indexes exist
CREATE INDEX IF NOT EXISTS idx_bookings_driver_id_status ON public.bookings(driver_id, status);
CREATE INDEX IF NOT EXISTS idx_bookings_status_created ON public.bookings(status, created_at DESC);

-- =====================================================
-- 2. ENSURE CONVERSATIONS TABLE HAS CORRECT STRUCTURE
-- =====================================================

-- Add missing columns to conversations table
ALTER TABLE public.conversations 
ADD COLUMN IF NOT EXISTS booking_id UUID,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS unread_count_driver INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS unread_count_passenger INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_message TEXT,
ADD COLUMN IF NOT EXISTS last_message_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add foreign key constraint for booking_id if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'conversations_booking_id_fkey'
    ) THEN
        ALTER TABLE public.conversations 
        ADD CONSTRAINT conversations_booking_id_fkey
        FOREIGN KEY (booking_id) REFERENCES public.bookings(id) ON DELETE SET NULL;
    END IF;
END $$;

-- =====================================================
-- 3. CREATE CRITICAL MISSING FUNCTION: get_driver_bookings
-- =====================================================

CREATE OR REPLACE FUNCTION get_driver_bookings(
    p_driver_id UUID,
    p_status TEXT DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    trip_id UUID,
    passenger_id UUID,
    driver_id UUID,
    seats_booked INTEGER,
    total_price DECIMAL,
    status TEXT,
    booking_type TEXT,
    message TEXT,
    special_requests TEXT,
    passenger_details JSONB,
    has_luggage BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    trip_data JSONB,
    passenger_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.id,
        b.trip_id,
        b.passenger_id,
        b.driver_id,
        b.seats_booked,
        b.total_price,
        b.status,
        COALESCE(b.booking_type, 'manual') as booking_type,
        b.message,
        b.special_requests,
        COALESCE(b.passenger_details, '{}'::jsonb) as passenger_details,
        COALESCE((b.passenger_details->>'has_luggage')::boolean, false) as has_luggage,
        b.created_at,
        b.confirmed_at,
        to_jsonb(t.*) as trip_data,
        to_jsonb(u.*) as passenger_data
    FROM public.bookings b
    LEFT JOIN public.trips t ON b.trip_id = t.id
    LEFT JOIN public.users u ON b.passenger_id = u.id
    WHERE b.driver_id = p_driver_id
    AND (p_status IS NULL OR b.status = p_status)
    ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. CREATE ENHANCED get_or_create_conversation FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION get_or_create_conversation(
    p_trip_id UUID,
    p_booking_id UUID DEFAULT NULL,
    p_driver_id UUID,
    p_passenger_id UUID
) RETURNS UUID AS $$
DECLARE
    conversation_id UUID;
BEGIN
    -- Try to find existing conversation
    SELECT id INTO conversation_id
    FROM public.conversations
    WHERE trip_id = p_trip_id 
    AND passenger_id = p_passenger_id
    LIMIT 1;
    
    -- If not found, create new conversation
    IF conversation_id IS NULL THEN
        INSERT INTO public.conversations (
            trip_id, 
            booking_id, 
            driver_id, 
            passenger_id,
            unread_count_driver,
            unread_count_passenger,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            p_trip_id,
            p_booking_id,
            p_driver_id,
            p_passenger_id,
            0,
            0,
            true,
            NOW(),
            NOW()
        ) RETURNING id INTO conversation_id;
        
        RAISE NOTICE 'Created new conversation % for trip % between driver % and passenger %', 
            conversation_id, p_trip_id, p_driver_id, p_passenger_id;
    ELSE
        -- Update booking_id if provided and not already set
        IF p_booking_id IS NOT NULL THEN
            UPDATE public.conversations 
            SET booking_id = p_booking_id,
                updated_at = NOW()
            WHERE id = conversation_id 
            AND booking_id IS NULL;
            
            RAISE NOTICE 'Updated conversation % with booking_id %', conversation_id, p_booking_id;
        END IF;
    END IF;
    
    RETURN conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 5. CREATE TRIGGER FOR AUTOMATIC CONVERSATION CREATION
-- =====================================================

CREATE OR REPLACE FUNCTION create_conversation_on_booking_acceptance()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create conversation when booking status changes to 'accepted'
    IF NEW.status = 'accepted' AND (OLD.status IS NULL OR OLD.status != 'accepted') THEN
        -- Create or get conversation using our function
        PERFORM get_or_create_conversation(
            NEW.trip_id,
            NEW.id,
            NEW.driver_id,
            NEW.passenger_id
        );
        
        RAISE NOTICE 'Conversation created/updated for accepted booking %', NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic conversation creation
DROP TRIGGER IF EXISTS trigger_create_conversation_on_booking_acceptance ON public.bookings;
CREATE TRIGGER trigger_create_conversation_on_booking_acceptance
    AFTER UPDATE ON public.bookings
    FOR EACH ROW
    EXECUTE FUNCTION create_conversation_on_booking_acceptance();

-- =====================================================
-- 6. CREATE HELPER FUNCTION FOR DASHBOARD STATISTICS
-- =====================================================

CREATE OR REPLACE FUNCTION get_driver_booking_counts(p_driver_id UUID)
RETURNS TABLE(
    pending_count INTEGER,
    accepted_count INTEGER,
    completed_count INTEGER,
    total_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(CASE WHEN b.status = 'pending' THEN 1 END)::INTEGER as pending_count,
        COUNT(CASE WHEN b.status = 'accepted' THEN 1 END)::INTEGER as accepted_count,
        COUNT(CASE WHEN b.status = 'completed' THEN 1 END)::INTEGER as completed_count,
        COUNT(*)::INTEGER as total_count
    FROM public.bookings b
    WHERE b.driver_id = p_driver_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 7. ENSURE PROPER RLS POLICIES
-- =====================================================

-- Enable RLS on bookings table
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Policy for drivers to see their own bookings
DROP POLICY IF EXISTS "Drivers can view their own bookings" ON public.bookings;
CREATE POLICY "Drivers can view their own bookings" ON public.bookings
    FOR SELECT USING (
        auth.uid() = driver_id OR 
        auth.uid() = passenger_id
    );

-- Policy for creating bookings
DROP POLICY IF EXISTS "Authenticated users can create bookings" ON public.bookings;
CREATE POLICY "Authenticated users can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (
        auth.uid() = passenger_id
    );

-- Policy for updating bookings (drivers can accept/reject)
DROP POLICY IF EXISTS "Drivers can update their bookings" ON public.bookings;
CREATE POLICY "Drivers can update their bookings" ON public.bookings
    FOR UPDATE USING (
        auth.uid() = driver_id OR 
        auth.uid() = passenger_id
    );

-- =====================================================
-- 8. VERIFY SETUP WITH TEST QUERY
-- =====================================================

-- This query should work without errors if everything is set up correctly
-- SELECT * FROM get_driver_bookings('test-driver-id', 'pending');

COMMENT ON FUNCTION get_driver_bookings(UUID, TEXT) IS 'Returns all bookings for a driver with optional status filter';
COMMENT ON FUNCTION get_or_create_conversation(UUID, UUID, UUID, UUID) IS 'Creates or retrieves conversation for a trip between driver and passenger';
COMMENT ON FUNCTION get_driver_booking_counts(UUID) IS 'Returns booking statistics for a driver dashboard';

-- =====================================================
-- SETUP COMPLETE
-- =====================================================
