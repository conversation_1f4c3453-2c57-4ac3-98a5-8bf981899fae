import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'lib/services/booking_service.dart';

/// Comprehensive test for the booking flow
class BookingFlowTest {
  static final _client = Supabase.instance.client;

  /// Run complete booking flow test
  static Future<void> runCompleteTest() async {
    if (!kDebugMode) {
      print('❌ Tests only run in debug mode');
      return;
    }

    print('🧪 Starting comprehensive booking flow test...');
    
    try {
      // Step 1: Get current driver
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        print('❌ No authenticated user');
        return;
      }

      final driverId = currentUser.id;
      print('👤 Testing with driver: $driverId');

      // Step 2: Clean up any existing test data
      await _cleanupTestData();

      // Step 3: Create test passenger
      await _createTestPassenger();

      // Step 4: Create test bookings
      await _createTestBookings(driverId);

      // Step 5: Verify bookings can be fetched
      await _verifyBookingsFetch(driverId);

      // Step 6: Test booking acceptance
      await _testBookingAcceptance();

      // Step 7: Verify conversation creation
      await _verifyConversationCreation();

      print('✅ All booking flow tests passed!');

    } catch (e) {
      print('❌ Booking flow test failed: $e');
    }
  }

  /// Clean up existing test data
  static Future<void> _cleanupTestData() async {
    print('🧹 Cleaning up existing test data...');

    // Delete test bookings
    await _client
        .from('bookings')
        .delete()
        .like('id', 'test-booking-%');

    // Delete test conversations
    await _client
        .from('conversations')
        .delete()
        .like('id', 'test-conv-%');

    // Delete test messages
    await _client
        .from('messages')
        .delete()
        .like('sender_id', 'test-passenger-%');

    print('✅ Test data cleaned up');
  }

  /// Create test passenger
  static Future<void> _createTestPassenger() async {
    print('👥 Creating test passenger...');

    await _client.from('users').upsert({
      'id': 'test-passenger-001',
      'email': '<EMAIL>',
      'full_name': 'فاطمة الزهراء',
      'phone': '+212612345678',
      'role': 'passenger',
      'city': 'الرباط',
      'rating': 4.8,
      'total_ratings': 25,
      'total_trips': 12,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    print('✅ Test passenger created');
  }

  /// Create test bookings
  static Future<void> _createTestBookings(String driverId) async {
    print('📝 Creating test bookings...');

    final testBookings = [
      {
        'id': 'test-booking-001',
        'trip_id': '1db7f4db-b209-4365-bda6-c2b9e3a3fddc', // From logs
        'passenger_id': 'test-passenger-001',
        'driver_id': driverId,
        'seats_booked': 1,
        'total_price': 220.0,
        'booking_type': 'manual',
        'status': 'pending',
        'message': 'أتطلع للرحلة معكم، أرجو قبول طلب الحجز',
        'special_requests': 'مقعد بجانب النافذة إذا أمكن',
        'passenger_details': {
          'passengers': [
            {
              'name': 'فاطمة الزهراء',
              'phone': '+212612345678',
              'age': 28,
            }
          ]
        },
        'is_paid': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'test-booking-002',
        'trip_id': '9456c729-c0c2-4897-93c8-cc86d0146c91', // From logs
        'passenger_id': 'test-passenger-001',
        'driver_id': driverId,
        'seats_booked': 2,
        'total_price': 150.0,
        'booking_type': 'manual',
        'status': 'pending',
        'message': 'هل يمكنني حجز مقعدين للرحلة؟',
        'special_requests': 'نحتاج مساحة إضافية للأمتعة',
        'passenger_details': {
          'passengers': [
            {
              'name': 'فاطمة الزهراء',
              'phone': '+212612345678',
              'age': 28,
            },
            {
              'name': 'أحمد محمد',
              'phone': '+212687654321',
              'age': 35,
            }
          ]
        },
        'is_paid': false,
        'created_at': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
        'updated_at': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
      }
    ];

    for (final booking in testBookings) {
      await _client.from('bookings').insert(booking);
      print('✅ Created booking: ${booking['id']}');
    }

    print('✅ All test bookings created');
  }

  /// Verify bookings can be fetched
  static Future<void> _verifyBookingsFetch(String driverId) async {
    print('🔍 Verifying booking fetch...');

    // Test direct query
    final directResponse = await _client
        .from('bookings')
        .select('''
          *,
          trip:trips(*),
          passenger:users!passenger_id(*)
        ''')
        .eq('driver_id', driverId)
        .eq('status', 'pending')
        .order('created_at', ascending: false);

    print('📊 Direct query returned ${directResponse.length} bookings');

    if (directResponse.isEmpty) {
      throw Exception('No bookings found in direct query');
    }

    // Verify data structure
    final firstBooking = directResponse.first;
    if (firstBooking['trip'] == null) {
      throw Exception('Trip data missing from booking');
    }
    if (firstBooking['passenger'] == null) {
      throw Exception('Passenger data missing from booking');
    }

    print('✅ Booking fetch verification passed');
  }

  /// Test booking acceptance
  static Future<void> _testBookingAcceptance() async {
    print('✅ Testing booking acceptance...');

    // Accept the first test booking
    final bookingId = 'test-booking-001';
    
    await _client
        .from('bookings')
        .update({
          'status': 'accepted',
          'confirmed_at': DateTime.now().toIso8601String(),
        })
        .eq('id', bookingId);

    // Verify status change
    final updatedBooking = await _client
        .from('bookings')
        .select('status')
        .eq('id', bookingId)
        .single();

    if (updatedBooking['status'] != 'accepted') {
      throw Exception('Booking status not updated');
    }

    print('✅ Booking acceptance test passed');
  }

  /// Verify conversation creation
  static Future<void> _verifyConversationCreation() async {
    print('💬 Verifying conversation creation...');

    // Create a test conversation
    final conversationId = 'test-conv-001';
    await _client.from('conversations').insert({
      'id': conversationId,
      'trip_id': '1db7f4db-b209-4365-bda6-c2b9e3a3fddc',
      'booking_id': 'test-booking-001',
      'driver_id': _client.auth.currentUser!.id,
      'passenger_id': 'test-passenger-001',
      'last_message': 'تم قبول طلب الحجز!',
      'last_message_at': DateTime.now().toIso8601String(),
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    // Verify conversation exists
    final conversation = await _client
        .from('conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

    if (conversation == null) {
      throw Exception('Conversation not created');
    }

    print('✅ Conversation creation verification passed');
  }

  /// Print test summary
  static void printTestSummary() {
    print('\n🎉 BOOKING FLOW TEST SUMMARY:');
    print('================================');
    print('✅ Test passenger created');
    print('✅ Test bookings created (2)');
    print('✅ Booking fetch verified');
    print('✅ Booking acceptance tested');
    print('✅ Conversation creation verified');
    print('✅ All tests passed successfully!');
    print('\n📱 Next steps:');
    print('1. Navigate to Driver Dashboard');
    print('2. Check "Requests" tab for test bookings');
    print('3. Test Accept/Reject buttons');
    print('4. Check "Chats" tab for conversations');
    print('================================\n');
  }
}
