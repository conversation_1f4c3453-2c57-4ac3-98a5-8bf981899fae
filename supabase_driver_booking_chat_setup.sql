-- =====================================================
-- SAFARNI DRIVER BOOKING → CHAT FLOW SQL SETUP
-- Run this in Supabase SQL Editor
-- =====================================================

-- 1) Create conversations table (if not exists)
CREATE TABLE IF NOT EXISTS public.conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id uuid,                              -- optional link to a booking
  trip_id uuid,
  driver_id uuid REFERENCES public.users(id),
  passenger_id uuid REFERENCES public.users(id),
  last_message text,
  unread_count_driver integer DEFAULT 0,
  unread_count_passenger integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 2) Create notifications table (if not exists)
CREATE TABLE IF NOT EXISTS public.notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.users(id),
  title text,
  body text,
  data jsonb,
  is_read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- 3) Provide a get_or_create_conversation RPC that finds or creates conversation row
CREATE OR REPLACE FUNCTION public.get_or_create_conversation(
  p_booking_id uuid,
  p_driver_id uuid,
  p_passenger_id uuid,
  p_trip_id uuid
) RETURNS TABLE(conversation_id uuid) AS $$
DECLARE
  c_id uuid;
BEGIN
  SELECT id INTO c_id FROM public.conversations
    WHERE booking_id = p_booking_id
      OR (driver_id = p_driver_id AND passenger_id = p_passenger_id AND trip_id = p_trip_id)
    LIMIT 1;

  IF FOUND THEN
    conversation_id := c_id;
    RETURN NEXT;
    RETURN;
  END IF;

  INSERT INTO public.conversations (booking_id, trip_id, driver_id, passenger_id)
  VALUES (p_booking_id, p_trip_id, p_driver_id, p_passenger_id)
  RETURNING id INTO c_id;

  conversation_id := c_id;
  RETURN NEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4) (Optional) Ensure bookings table has required columns (run only if missing)
ALTER TABLE public.bookings
  ADD COLUMN IF NOT EXISTS driver_id uuid,
  ADD COLUMN IF NOT EXISTS passenger_details jsonb,
  ADD COLUMN IF NOT EXISTS status text,
  ADD COLUMN IF NOT EXISTS total_price numeric,
  ADD COLUMN IF NOT EXISTS special_requests text;

-- 5) Add triggers to update updated_at on conversations table
CREATE OR REPLACE FUNCTION public.set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS conversations_set_timestamp ON public.conversations;
CREATE TRIGGER conversations_set_timestamp
  BEFORE UPDATE ON public.conversations
  FOR EACH ROW EXECUTE FUNCTION public.set_timestamp();

-- 6) Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversations_driver_id ON public.conversations(driver_id);
CREATE INDEX IF NOT EXISTS idx_conversations_passenger_id ON public.conversations(passenger_id);
CREATE INDEX IF NOT EXISTS idx_conversations_booking_id ON public.conversations(booking_id);
CREATE INDEX IF NOT EXISTS idx_conversations_trip_id ON public.conversations(trip_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(user_id, is_read);

-- 7) Enable RLS on new tables
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- 8) Create RLS policies for conversations
CREATE POLICY "Users can view their own conversations" ON public.conversations
  FOR SELECT USING (
    auth.uid() = driver_id OR auth.uid() = passenger_id
  );

CREATE POLICY "Users can insert conversations they participate in" ON public.conversations
  FOR INSERT WITH CHECK (
    auth.uid() = driver_id OR auth.uid() = passenger_id
  );

CREATE POLICY "Users can update their own conversations" ON public.conversations
  FOR UPDATE USING (
    auth.uid() = driver_id OR auth.uid() = passenger_id
  );

-- 9) Create RLS policies for notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert notifications" ON public.notifications
  FOR INSERT WITH CHECK (true);

-- 10) Grant permissions
GRANT EXECUTE ON FUNCTION public.get_or_create_conversation TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.conversations TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;

-- =====================================================
-- VERIFICATION QUERIES (run these to check setup)
-- =====================================================

-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('conversations', 'notifications');

-- Check if function exists
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'get_or_create_conversation';

-- Check conversations table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'conversations' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check notifications table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'notifications' 
AND table_schema = 'public'
ORDER BY ordinal_position;
