import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../models/booking_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/booking_service.dart';
import '../../services/notification_service.dart';
import 'booking_status_card.dart';

class MyBookingsPage extends StatefulWidget {
  const MyBookingsPage({super.key});

  @override
  State<MyBookingsPage> createState() => _MyBookingsPageState();
}

class _MyBookingsPageState extends State<MyBookingsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<BookingModel> _allBookings = [];
  List<BookingModel> _pendingBookings = [];
  List<BookingModel> _acceptedBookings = [];
  List<BookingModel> _completedBookings = [];
  List<BookingModel> _rejectedBookings = [];
  
  bool _isLoading = true;
  RealtimeChannel? _bookingChannel;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadBookings();
    _setupRealtimeSubscriptions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _cleanupSubscriptions();
    super.dispose();
  }

  Future<void> _loadBookings() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final bookings = await BookingService.getPassengerBookings(currentUser.id);
      
      if (mounted) {
        setState(() {
          _allBookings = bookings;
          _pendingBookings = bookings.where((b) => b.status == 'pending').toList();
          _acceptedBookings = bookings.where((b) => b.status == 'accepted').toList();
          _completedBookings = bookings.where((b) => b.status == 'completed').toList();
          _rejectedBookings = bookings.where((b) => b.status == 'rejected' || b.status == 'cancelled').toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل الحجوزات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _setupRealtimeSubscriptions() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return;

    // Use the enhanced booking service subscription
    _bookingChannel = BookingService.subscribeToPassengerBookings(
      passengerId: currentUser.id,
      onBookingUpdate: (booking) {
        if (mounted) {
          // Update the specific booking in our lists
          _updateBookingInLists(booking);
        }
      },
      onError: (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث الحجوزات: $error'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
    );
  }

  void _updateBookingInLists(BookingModel updatedBooking) {
    setState(() {
      // Remove from all lists first
      _allBookings.removeWhere((b) => b.id == updatedBooking.id);
      _pendingBookings.removeWhere((b) => b.id == updatedBooking.id);
      _acceptedBookings.removeWhere((b) => b.id == updatedBooking.id);
      _completedBookings.removeWhere((b) => b.id == updatedBooking.id);
      _rejectedBookings.removeWhere((b) => b.id == updatedBooking.id);

      // Add to appropriate list based on status
      _allBookings.add(updatedBooking);
      switch (updatedBooking.status) {
        case 'pending':
          _pendingBookings.add(updatedBooking);
          break;
        case 'accepted':
          _acceptedBookings.add(updatedBooking);
          break;
        case 'completed':
          _completedBookings.add(updatedBooking);
          break;
        case 'rejected':
        case 'cancelled':
          _rejectedBookings.add(updatedBooking);
          break;
      }

      // Sort lists by creation date (newest first)
      _allBookings.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      _pendingBookings.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      _acceptedBookings.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      _completedBookings.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      _rejectedBookings.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    });
  }

  void _cleanupSubscriptions() {
    if (_bookingChannel != null) {
      BookingService.unsubscribeFromBookings(_bookingChannel!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حجوزاتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadBookings,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: AppColors.primary,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              isScrollable: true,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.pending, size: 18),
                      const SizedBox(width: 8),
                      const Text('معلقة'),
                      if (_pendingBookings.isNotEmpty) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.warning,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_pendingBookings.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.check_circle, size: 18),
                      const SizedBox(width: 8),
                      const Text('مقبولة'),
                      if (_acceptedBookings.isNotEmpty) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.success,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_acceptedBookings.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.done_all, size: 18),
                      const SizedBox(width: 8),
                      const Text('مكتملة'),
                      if (_completedBookings.isNotEmpty) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.info,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_completedBookings.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.cancel, size: 18),
                      const SizedBox(width: 8),
                      const Text('مرفوضة'),
                      if (_rejectedBookings.isNotEmpty) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_rejectedBookings.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tab Views
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _BookingsList(
                        bookings: _pendingBookings,
                        emptyTitle: 'لا توجد حجوزات معلقة',
                        emptySubtitle: 'ستظهر حجوزاتك في انتظار الموافقة هنا',
                        emptyIcon: Icons.pending_outlined,
                        onBookingUpdated: _updateBookingInLists,
                      ),
                      _BookingsList(
                        bookings: _acceptedBookings,
                        emptyTitle: 'لا توجد حجوزات مقبولة',
                        emptySubtitle: 'ستظهر حجوزاتك المقبولة هنا',
                        emptyIcon: Icons.check_circle_outline,
                        onBookingUpdated: _updateBookingInLists,
                      ),
                      _BookingsList(
                        bookings: _completedBookings,
                        emptyTitle: 'لا توجد رحلات مكتملة',
                        emptySubtitle: 'ستظهر رحلاتك المكتملة هنا',
                        emptyIcon: Icons.done_all_outlined,
                        onBookingUpdated: _updateBookingInLists,
                      ),
                      _BookingsList(
                        bookings: _rejectedBookings,
                        emptyTitle: 'لا توجد حجوزات مرفوضة',
                        emptySubtitle: 'ستظهر حجوزاتك المرفوضة هنا',
                        emptyIcon: Icons.cancel_outlined,
                        onBookingUpdated: _updateBookingInLists,
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
}

class _BookingsList extends StatelessWidget {
  final List<BookingModel> bookings;
  final String emptyTitle;
  final String emptySubtitle;
  final IconData emptyIcon;
  final Function(BookingModel)? onBookingUpdated;

  const _BookingsList({
    required this.bookings,
    required this.emptyTitle,
    required this.emptySubtitle,
    required this.emptyIcon,
    this.onBookingUpdated,
  });

  @override
  Widget build(BuildContext context) {
    if (bookings.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        return BookingStatusCard(
          booking: bookings[index],
          onBookingUpdated: onBookingUpdated,
          onTap: () {
            // Navigate to booking details or chat
          },
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            emptyIcon,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            emptyTitle,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            emptySubtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textTertiary,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
