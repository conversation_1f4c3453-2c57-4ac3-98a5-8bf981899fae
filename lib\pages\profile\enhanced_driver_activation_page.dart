import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart' as provider;
// import 'package:image_picker/image_picker.dart';  // Commented out for web compatibility
import 'package:file_picker/file_picker.dart';
// import 'package:file_picker/file_picker.dart';  // Temporarily disabled
import 'package:flutter/foundation.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/animated_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/web_image_preview.dart';
import '../../widgets/safe_network_image.dart';
import '../../services/profile_image_service.dart';
import '../../services/wallet_service.dart';

class EnhancedDriverActivationPage extends StatefulWidget {
  const EnhancedDriverActivationPage({super.key});

  @override
  State<EnhancedDriverActivationPage> createState() =>
      _EnhancedDriverActivationPageState();
}

class _EnhancedDriverActivationPageState
    extends State<EnhancedDriverActivationPage> with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  bool _isLoading = false;

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _progressController;
  late AnimationController _buttonController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _progressAnimation;
  late Animation<double> _buttonScaleAnimation;
  late Animation<Color?> _buttonColorAnimation;

  // Step 1: Personal Information (pre-filled from existing data)
  final _nameController = TextEditingController();
  PlatformFile? _profileImage;

  // Step 2: Driver's License
  final _licenseNumberController = TextEditingController();
  PlatformFile? _licenseImage;

  // Step 3: Vehicle Information
  final _vehicleMakeController = TextEditingController();
  final _vehicleModelController = TextEditingController();
  final _vehicleColorController = TextEditingController();
  final _vehiclePlateController = TextEditingController();
  final _vehicleSeatsController = TextEditingController(text: '4');
  PlatformFile? _vehicleImage;

  // Step 4: Payment Information
  final _bankAccountController = TextEditingController();
  final _bankNameController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // Setup animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
        CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));

    _progressAnimation = Tween<double>(begin: 0.0, end: 0.25).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );

    _buttonScaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _buttonController, curve: Curves.easeInOut),
    );

    _buttonColorAnimation = ColorTween(
      begin: AppColors.textSecondary,
      end: AppColors.primary,
    ).animate(
        CurvedAnimation(parent: _buttonController, curve: Curves.easeInOut));

    // Load existing user data
    _loadExistingData();

    // Start animations
    _fadeController.forward();
    _slideController.forward();
    _progressController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _progressController.dispose();
    _buttonController.dispose();
    _pageController.dispose();

    // Dispose text controllers
    _nameController.dispose();
    _licenseNumberController.dispose();
    _vehicleMakeController.dispose();
    _vehicleModelController.dispose();
    _vehicleColorController.dispose();
    _vehiclePlateController.dispose();
    _vehicleSeatsController.dispose();
    _bankAccountController.dispose();
    _bankNameController.dispose();

    super.dispose();
  }

  void _loadExistingData() {
    final authProvider =
        provider.Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser != null) {
      _nameController.text = currentUser.fullName;

      // Check if data is complete and enable button
      if (_nameController.text.isNotEmpty) {
        _buttonController.forward();

        // Light vibration to indicate data is ready
        HapticFeedback.lightImpact();
      }
    }
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0: // Personal Information
        return _nameController.text.isNotEmpty;
      case 1: // Driver's License
        return _licenseImage != null;
      case 2: // Vehicle Information
        return _vehicleMakeController.text.isNotEmpty &&
            _vehicleModelController.text.isNotEmpty &&
            _vehicleColorController.text.isNotEmpty &&
            _vehiclePlateController.text.isNotEmpty;
      case 3: // Payment Information
        return _bankAccountController.text.isNotEmpty &&
            _bankNameController.text.isNotEmpty;
      default:
        return false;
    }
  }

  void _nextStep() async {
    if (!_canProceed()) return;

    // Haptic feedback
    HapticFeedback.mediumImpact();

    if (_currentStep < 3) {
      setState(() => _currentStep++);

      // Update progress animation
      final newProgress = (_currentStep + 1) * 0.25;
      _progressController.animateTo(newProgress);

      // Slide to next page
      await _pageController.nextPage(
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOutCubic,
      );

      // Reset and check button state for new step
      _buttonController.reset();
      if (_canProceed()) {
        _buttonController.forward();
      }
    } else {
      // Final step - activate driver mode
      await _activateDriverMode();
    }
  }

  Future<void> _activateDriverMode() async {
    setState(() => _isLoading = true);

    try {
      final authProvider =
          provider.Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser == null) {
        _showErrorSnackBar('خطأ في المصادقة');
        return;
      }

      // Step 1: Save Personal Information
      await _savePersonalInfo(currentUser, authProvider);

      // Step 2: Save Driver License
      await _saveDriverLicense(currentUser.id);

      // Step 3: Save Vehicle Information
      await _saveVehicleInfo(currentUser.id);

      // Step 4: Process Payment (simulate for now)
      await _savePaymentInfo(currentUser.id);

      // Update user to driver status
      final updatedUser = currentUser.copyWith(
        isLeader: true,
        role: 'trip_leader',
      );
      await authProvider.updateProfile(updatedUser);

      _showSuccessSnackBar('تم تفعيل وضع السائق بنجاح! 🎉');

      // Navigate back with celebration animation
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Driver activation error: $e');
      }
      _showErrorSnackBar('حدث خطأ أثناء تفعيل وضع السائق');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _savePersonalInfo(
      dynamic currentUser, AuthProvider authProvider) async {
    String? profileImageUrl;

    // Upload profile image if selected
    if (_profileImage != null) {
      profileImageUrl = await ProfileImageService.uploadProfileImage(
        imageFile: _profileImage!,
        userId: currentUser.id,
      );

      if (profileImageUrl == null) {
        throw Exception('فشل في رفع الصورة الشخصية');
      }
    }

    // Update user profile
    final updateData = <String, dynamic>{
      'full_name': _nameController.text.trim(),
    };

    if (profileImageUrl != null) {
      updateData['profile_image_url'] = profileImageUrl;
    }

    final updatedUser = currentUser.copyWith(
      fullName: updateData['full_name'],
      profileImageUrl: updateData['profile_image_url'],
    );
    await authProvider.updateProfile(updatedUser);

    if (kDebugMode) {
      print('✅ Personal info saved successfully');
    }
  }

  Future<void> _saveDriverLicense(String userId) async {
    if (_licenseImage == null) {
      throw Exception('يرجى رفع صورة رخصة القيادة');
    }

    // Upload license image to private driver-licenses bucket
    final licenseImagePath = await StorageService.uploadDriverLicense(
      imageFile: _licenseImage!,
      userId: userId,
    );

    if (licenseImagePath == null) {
      throw Exception('فشل في رفع صورة رخصة القيادة');
    }

    // Save license data to Supabase database
    try {
      final supabase = Supabase.instance.client;

      // Create or update driver_licenses table entry
      await supabase.from('driver_licenses').upsert({
        'user_id': userId,
        'license_number': _licenseNumberController.text.trim().isEmpty
            ? null
            : _licenseNumberController.text.trim(),
        'license_image_path':
            licenseImagePath, // Store path for signed URL generation
        'verified': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('✅ License saved to database: $licenseImagePath');
      }
    } catch (e) {
      throw Exception('فشل في حفظ بيانات رخصة القيادة: ${e.toString()}');
    }
  }

  Future<void> _saveVehicleInfo(String userId) async {
    String? vehicleImageUrl;

    // Upload vehicle image if selected
    if (_vehicleImage != null) {
      vehicleImageUrl = await StorageService.uploadCarImage(
        imageFile: _vehicleImage!,
        userId: userId,
      );
    }

    // Save vehicle data to Supabase database
    try {
      final supabase = Supabase.instance.client;

      await supabase.from('vehicles').upsert({
        'user_id': userId,
        'make': _vehicleMakeController.text.trim(),
        'model': _vehicleModelController.text.trim(),
        'color': _vehicleColorController.text.trim(),
        'plate_number': _vehiclePlateController.text.trim(),
        'seats': int.tryParse(_vehicleSeatsController.text) ?? 4,
        'image_url': vehicleImageUrl,
        'verified': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('✅ Vehicle info saved successfully');
      }
    } catch (e) {
      throw Exception('فشل في حفظ بيانات المركبة: ${e.toString()}');
    }
  }

  Future<void> _savePaymentInfo(String userId) async {
    try {
      // Save payment info to database (simplified for now)
      final supabase = Supabase.instance.client;

      await supabase.from('driver_payments').upsert({
        'user_id': userId,
        'bank_account': _bankAccountController.text.trim(),
        'bank_name': _bankNameController.text.trim(),
        'verified': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('✅ Payment info saved successfully');
      }
    } catch (e) {
      throw Exception('فشل في إعداد معلومات الدفع: ${e.toString()}');
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = provider.Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: AppColors.textPrimary,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'تفعيل وضع السائق',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary.withValues(alpha: 0.03),
              AppColors.secondary.withValues(alpha: 0.01),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Progress Bar
              _buildProgressBar(),

              // Content
              Expanded(
                child: AnimatedBuilder(
                  animation:
                      Listenable.merge([_fadeAnimation, _slideAnimation]),
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: PageView(
                          controller: _pageController,
                          physics: const NeverScrollableScrollPhysics(),
                          children: [
                            _buildPersonalInfoStep(currentUser),
                            _buildLicenseStep(),
                            _buildVehicleStep(),
                            _buildPaymentStep(),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Next Button
              _buildNextButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الخطوة ${_currentStep + 1} من 4',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return Text(
                    '${(_progressAnimation.value * 100).round()}%',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 12),
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return Container(
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: AppColors.surfaceVariant,
                ),
                child: Stack(
                  children: [
                    Container(
                      height: 8,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        gradient: AppColors.primaryGradient,
                      ),
                      width: MediaQuery.of(context).size.width *
                          _progressAnimation.value,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoStep(dynamic currentUser) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome message
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withValues(alpha: 0.1),
                  AppColors.secondary.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: [
                Text(
                  'مرحباً ${currentUser?.fullName ?? ''}! 👋',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'أنت على بُعد خطوات قليلة من تفعيل وضع السائق.\nتأكد من صحة المعلومات المعروضة',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                        height: 1.5,
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Profile Image Section
          _buildProfileImageSection(),

          const SizedBox(height: 24),

          // Name Field
          Text(
            'الاسم الكامل',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
          ),
          const SizedBox(height: 8),
          CustomTextField(
            controller: _nameController,
            label: 'الاسم الكامل',
            hint: 'أدخل اسمك الكامل',
            prefixIcon: Icons.person_rounded,
            onChanged: (value) {
              setState(() {});
              if (_canProceed()) {
                _buttonController.forward();
                HapticFeedback.selectionClick();
              } else {
                _buttonController.reverse();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImageSection() {
    return Center(
      child: GestureDetector(
        onTap: _showImageSourceDialog,
        child: Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: _profileImage != null
                ? null
                : LinearGradient(
                    colors: [
                      AppColors.primary.withValues(alpha: 0.1),
                      AppColors.secondary.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            border: Border.all(
              color: _profileImage != null
                  ? AppColors.success.withValues(alpha: 0.6)
                  : AppColors.primary.withValues(alpha: 0.3),
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: (_profileImage != null
                        ? AppColors.success
                        : AppColors.primary)
                    .withValues(alpha: 0.2),
                blurRadius: 15,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: _profileImage != null
              ? ClipOval(
                  child: WebImagePreview(
                    imageFile: _profileImage,
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                  ),
                )
              : provider.Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    final currentUser = authProvider.currentUser;
                    return ProfileAvatar(
                      imageUrl: currentUser != null
                          ? StorageService.getProfileImageUrl(
                              currentUser.id,
                              storedUrl: currentUser.profileImageUrl,
                            )
                          : null,
                      radius: 60,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLicenseStep() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'رخصة القيادة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى رفع صورة واضحة لرخصة القيادة الخاصة بك',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                  height: 1.5,
                ),
          ),
          const SizedBox(height: 32),

          // License Image Upload
          _buildLicenseImageSection(),

          const SizedBox(height: 24),

          // License Number (Optional)
          Text(
            'رقم الرخصة (اختياري)',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
          ),
          const SizedBox(height: 8),
          CustomTextField(
            controller: _licenseNumberController,
            label: 'رقم الرخصة',
            hint: 'أدخل رقم رخصة القيادة',
            prefixIcon: Icons.credit_card_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildLicenseImageSection() {
    return Center(
      child: GestureDetector(
        onTap: _showImageSourceDialog,
        child: Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            gradient: _licenseImage != null
                ? null
                : LinearGradient(
                    colors: [
                      AppColors.accent.withValues(alpha: 0.1),
                      AppColors.accent.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _licenseImage != null
                  ? AppColors.success.withValues(alpha: 0.5)
                  : AppColors.accent.withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.accent.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: _licenseImage != null
              ? Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(14),
                      child: WebImagePreview(
                        imageFile: _licenseImage,
                        width: double.infinity,
                        height: 200,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.success.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.check_rounded,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.credit_card_rounded,
                      size: 48,
                      color: AppColors.accent.withValues(alpha: 0.7),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'اضغط لإضافة صورة رخصة القيادة',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.textSecondary.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'اختر مصدر الصورة',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildSourceOption(
                    icon: Icons.camera_alt_rounded,
                    label: 'الكاميرا',
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage();
                    },
                  ),
                  _buildSourceOption(
                    icon: Icons.photo_library_rounded,
                    label: 'المعرض',
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 120,
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: AppColors.primary,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final pickedFile = result.files.first;

        // Check file size (max 5MB)
        if (pickedFile.bytes != null && pickedFile.bytes!.length > 5 * 1024 * 1024) {
          _showErrorSnackBar('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
          return;
        }

        setState(() {
          if (_currentStep == 1) {
            _licenseImage = pickedFile;
          } else if (_currentStep == 0) {
            _profileImage = pickedFile;
          } else if (_currentStep == 2) {
            _vehicleImage = pickedFile;
          }
        });

        // Check if can proceed and update button
        if (_canProceed()) {
          _buttonController.forward();
          HapticFeedback.selectionClick();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking image: $e');
      }
      _showErrorSnackBar('فشل في اختيار الصورة');
    }
  }

  Widget _buildVehicleStep() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المركبة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'أدخل تفاصيل مركبتك التي ستستخدمها في الرحلات',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                    height: 1.5,
                  ),
            ),
            const SizedBox(height: 24),

            // Vehicle Make
            CustomTextField(
              controller: _vehicleMakeController,
              label: 'الماركة',
              hint: 'مثل: تويوتا، نيسان، هيونداي',
              prefixIcon: Icons.directions_car_rounded,
              onChanged: (value) => _updateButtonState(),
            ),
            const SizedBox(height: 16),

            // Vehicle Model
            CustomTextField(
              controller: _vehicleModelController,
              label: 'الموديل',
              hint: 'مثل: كامري، التيما، إلنترا',
              prefixIcon: Icons.car_rental_rounded,
              onChanged: (value) => _updateButtonState(),
            ),
            const SizedBox(height: 16),

            // Vehicle Color
            CustomTextField(
              controller: _vehicleColorController,
              label: 'اللون',
              hint: 'مثل: أبيض، أسود، فضي',
              prefixIcon: Icons.palette_rounded,
              onChanged: (value) => _updateButtonState(),
            ),
            const SizedBox(height: 16),

            // Vehicle Plate
            CustomTextField(
              controller: _vehiclePlateController,
              label: 'رقم اللوحة',
              hint: 'أدخل رقم لوحة المركبة',
              prefixIcon: Icons.confirmation_number_rounded,
              onChanged: (value) => _updateButtonState(),
            ),
            const SizedBox(height: 16),

            // Vehicle Seats
            CustomTextField(
              controller: _vehicleSeatsController,
              label: 'عدد المقاعد',
              hint: '4',
              prefixIcon: Icons.event_seat_rounded,
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentStep() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الدفع',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'أدخل معلومات حسابك البنكي لاستلام الأرباح',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                  height: 1.5,
                ),
          ),
          const SizedBox(height: 32),

          // Bank Name
          CustomTextField(
            controller: _bankNameController,
            label: 'اسم البنك',
            hint: 'مثل: البنك الأهلي، بنك الراجحي',
            prefixIcon: Icons.account_balance_rounded,
            onChanged: (value) => _updateButtonState(),
          ),
          const SizedBox(height: 16),

          // Bank Account
          CustomTextField(
            controller: _bankAccountController,
            label: 'رقم الحساب',
            hint: 'أدخل رقم حسابك البنكي',
            prefixIcon: Icons.credit_card_rounded,
            keyboardType: TextInputType.number,
            onChanged: (value) => _updateButtonState(),
          ),
        ],
      ),
    );
  }

  Widget _buildNextButton() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: AnimatedBuilder(
        animation:
            Listenable.merge([_buttonScaleAnimation, _buttonColorAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _buttonScaleAnimation.value,
            child: AnimatedSwipeButton(
              text: _currentStep == 3 ? 'تفعيل وضع السائق' : 'التالي',
              onPressed: _canProceed() && !_isLoading ? _nextStep : null,
              isLoading: _isLoading,
              isEnabled: _canProceed(),
              icon: _currentStep == 3
                  ? Icons.check_circle_rounded
                  : Icons.arrow_forward_rounded,
              backgroundColor:
                  _canProceed() ? AppColors.primary : AppColors.textSecondary,
              width: double.infinity,
              height: 56,
            ),
          );
        },
      ),
    );
  }

  void _updateButtonState() {
    setState(() {});
    if (_canProceed()) {
      _buttonController.forward();
      HapticFeedback.selectionClick();
    } else {
      _buttonController.reverse();
    }
  }
}
