import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_theme.dart';
import '../providers/auth_provider.dart';
import '../services/navigation_service.dart';
import '../services/notification_service.dart';
import '../services/message_service.dart';

class EnhancedBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;

  const EnhancedBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  State<EnhancedBottomNavigation> createState() => _EnhancedBottomNavigationState();
}

class _EnhancedBottomNavigationState extends State<EnhancedBottomNavigation>
    with TickerProviderStateMixin {
  int _unreadMessagesCount = 0;
  int _unreadNotificationsCount = 0;
  late AnimationController _badgeAnimationController;
  late Animation<double> _badgeAnimation;

  @override
  void initState() {
    super.initState();
    _badgeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _badgeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _badgeAnimationController,
      curve: Curves.elasticOut,
    ));
    
    _loadUnreadCounts();
    _setupRealtimeUpdates();
  }

  @override
  void dispose() {
    _badgeAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadUnreadCounts() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    try {
      // Load unread notifications count
      final notificationsCount = await NotificationService.getUnreadCount(currentUser.id);

      // Load unread messages count
      final messagesCount = await MessageService.getUnreadMessagesCount(currentUser.id);
      
      if (mounted) {
        setState(() {
          _unreadNotificationsCount = notificationsCount;
          _unreadMessagesCount = messagesCount;
        });
        
        if (notificationsCount > 0 || messagesCount > 0) {
          _badgeAnimationController.forward();
        }
      }
    } catch (e) {
      // Handle error silently
    }
  }

  void _setupRealtimeUpdates() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return;

    // Subscribe to notification updates
    NotificationService.subscribeToUserNotifications(
      currentUser.id,
      (notification) {
        if (mounted) {
          setState(() {
            _unreadNotificationsCount++;
          });
          _badgeAnimationController.forward();
        }
      },
    );

    // Subscribe to conversation updates to refresh message count
    Supabase.instance.client
        .channel('conversations_badge:${currentUser.id}')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'conversations',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'driver_id',
            value: currentUser.id,
          ),
          callback: (payload) async {
            if (mounted) {
              final newCount = await MessageService.getUnreadMessagesCount(currentUser.id);
              setState(() {
                _unreadMessagesCount = newCount;
              });
              if (newCount > 0) {
                _badgeAnimationController.forward();
              }
            }
          },
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'conversations',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'passenger_id',
            value: currentUser.id,
          ),
          callback: (payload) async {
            if (mounted) {
              final newCount = await MessageService.getUnreadMessagesCount(currentUser.id);
              setState(() {
                _unreadMessagesCount = newCount;
              });
              if (newCount > 0) {
                _badgeAnimationController.forward();
              }
            }
          },
        )
        .subscribe();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isDriver = authProvider.currentUser?.isLeader == true;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 65,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                index: 0,
                icon: Icons.home_outlined,
                activeIcon: Icons.home,
                label: 'الرئيسية',
              ),
              _buildNavItem(
                index: 1,
                icon: Icons.search_outlined,
                activeIcon: Icons.search,
                label: 'البحث',
              ),
              if (isDriver)
                _buildNavItem(
                  index: 2,
                  icon: Icons.assignment_outlined,
                  activeIcon: Icons.assignment,
                  label: 'الطلبات',
                  badgeCount: _unreadNotificationsCount,
                )
              else
                _buildNavItem(
                  index: 2,
                  icon: Icons.bookmark_outline,
                  activeIcon: Icons.bookmark,
                  label: 'حجوزاتي',
                ),
              _buildNavItem(
                index: 3,
                icon: Icons.chat_bubble_outline,
                activeIcon: Icons.chat_bubble,
                label: 'الرسائل',
                badgeCount: _unreadMessagesCount,
              ),
              _buildNavItem(
                index: 4,
                icon: Icons.person_outline,
                activeIcon: Icons.person,
                label: 'الملف',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
    int badgeCount = 0,
  }) {
    final isActive = widget.currentIndex == index;
    final color = isActive ? AppColors.primary : AppColors.textTertiary;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => widget.onTap(index),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    AnimatedContainer(
                      duration: AppColors.animationFast,
                      curve: AppColors.animationCurve,
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: isActive 
                            ? AppColors.primary.withValues(alpha: 0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        isActive ? activeIcon : icon,
                        color: color,
                        size: 24,
                      ),
                    ),
                    if (badgeCount > 0)
                      Positioned(
                        right: -2,
                        top: -2,
                        child: AnimatedBuilder(
                          animation: _badgeAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _badgeAnimation.value,
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: AppColors.error,
                                  shape: BoxShape.circle,
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 16,
                                  minHeight: 16,
                                ),
                                child: Text(
                                  badgeCount > 99 ? '99+' : badgeCount.toString(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                AnimatedDefaultTextStyle(
                  duration: AppColors.animationFast,
                  style: TextStyle(
                    color: color,
                    fontSize: 11,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                  ),
                  child: Text(
                    label,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void updateBadgeCounts({int? messages, int? notifications}) {
    if (mounted) {
      setState(() {
        if (messages != null) _unreadMessagesCount = messages;
        if (notifications != null) _unreadNotificationsCount = notifications;
      });
      
      if (_unreadMessagesCount > 0 || _unreadNotificationsCount > 0) {
        _badgeAnimationController.forward();
      } else {
        _badgeAnimationController.reverse();
      }
    }
  }
}

/// Extension to easily access the enhanced bottom navigation
extension EnhancedBottomNavigationExtension on State {
  void updateBottomNavBadges({int? messages, int? notifications}) {
    // This would be called from parent widgets to update badge counts
    // Implementation depends on how the parent manages the bottom navigation state
  }
}
