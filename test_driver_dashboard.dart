// Test script to verify driver dashboard booking functionality
// This will help identify why bookings aren't showing up

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class DriverDashboardTest {
  static final _client = Supabase.instance.client;

  static Future<void> testDriverBookingFlow() async {
    print('🧪 Testing Driver Dashboard Booking Flow');
    print('=' * 50);

    try {
      // Test 1: Check if get_driver_bookings function exists
      print('\n📋 Test 1: Testing get_driver_bookings function...');
      await _testDriverBookingsFunction();

      // Test 2: Test direct booking query
      print('\n📋 Test 2: Testing direct booking query...');
      await _testDirectBookingQuery();

      // Test 3: Test booking creation and retrieval
      print('\n📋 Test 3: Testing booking creation and retrieval...');
      await _testBookingCreationAndRetrieval();

      // Test 4: Test conversation creation
      print('\n📋 Test 4: Testing conversation creation...');
      await _testConversationCreation();

      print('\n✅ All tests completed!');

    } catch (e) {
      print('❌ Test failed with error: $e');
      print('Stack trace: ${StackTrace.current}');
    }
  }

  static Future<void> _testDriverBookingsFunction() async {
    try {
      // Test with a dummy driver ID
      final testDriverId = 'test-driver-id';
      
      final response = await _client.rpc('get_driver_bookings', params: {
        'p_driver_id': testDriverId,
        'p_status': 'pending',
      });

      print('✅ get_driver_bookings function exists and returned: ${response?.runtimeType}');
      print('   Response length: ${response is List ? response.length : 'Not a list'}');
      
    } catch (e) {
      print('❌ get_driver_bookings function error: $e');
      print('   This function might be missing from the database!');
    }
  }

  static Future<void> _testDirectBookingQuery() async {
    try {
      // Test direct query to bookings table
      final response = await _client
          .from('bookings')
          .select('''
            *,
            trip:trips(*),
            passenger:users!passenger_id(*)
          ''')
          .eq('status', 'pending')
          .limit(5);

      print('✅ Direct booking query successful');
      print('   Found ${response.length} pending bookings in total');
      
      if (response.isNotEmpty) {
        final firstBooking = response.first;
        print('   Sample booking ID: ${firstBooking['id']}');
        print('   Driver ID: ${firstBooking['driver_id']}');
        print('   Passenger ID: ${firstBooking['passenger_id']}');
        print('   Trip data: ${firstBooking['trip'] != null ? 'Present' : 'Missing'}');
        print('   Passenger data: ${firstBooking['passenger'] != null ? 'Present' : 'Missing'}');
      }
      
    } catch (e) {
      print('❌ Direct booking query error: $e');
    }
  }

  static Future<void> _testBookingCreationAndRetrieval() async {
    try {
      // First, check if there are any users and trips in the database
      final users = await _client
          .from('users')
          .select('id, full_name, role')
          .limit(2);

      final trips = await _client
          .from('trips')
          .select('id, driver_id, from_city, to_city, status')
          .eq('status', 'active')
          .limit(1);

      print('✅ Database check:');
      print('   Users found: ${users.length}');
      print('   Active trips found: ${trips.length}');

      if (users.length >= 2 && trips.isNotEmpty) {
        final driver = users.firstWhere((u) => u['role'] == 'trip_leader' || u['role'] == 'driver', 
                                       orElse: () => users.first);
        final passenger = users.firstWhere((u) => u['id'] != driver['id'], 
                                          orElse: () => users.last);
        final trip = trips.first;

        print('   Driver: ${driver['full_name']} (${driver['id']})');
        print('   Passenger: ${passenger['full_name']} (${passenger['id']})');
        print('   Trip: ${trip['from_city']} → ${trip['to_city']} (${trip['id']})');

        // Test fetching bookings for this driver
        final driverBookings = await _client
            .from('bookings')
            .select('''
              *,
              trip:trips(*),
              passenger:users!passenger_id(*)
            ''')
            .eq('driver_id', driver['id']);

        print('   Bookings for this driver: ${driverBookings.length}');
        
        if (driverBookings.isNotEmpty) {
          final booking = driverBookings.first;
          print('   Sample booking status: ${booking['status']}');
          print('   Sample booking created: ${booking['created_at']}');
        }
      } else {
        print('   ⚠️ Not enough test data in database to test booking flow');
      }

    } catch (e) {
      print('❌ Booking creation/retrieval test error: $e');
    }
  }

  static Future<void> _testConversationCreation() async {
    try {
      // Test get_or_create_conversation function
      final testTripId = 'test-trip-id';
      final testDriverId = 'test-driver-id';
      final testPassengerId = 'test-passenger-id';
      
      final conversationId = await _client.rpc('get_or_create_conversation', params: {
        'p_trip_id': testTripId,
        'p_booking_id': null,
        'p_driver_id': testDriverId,
        'p_passenger_id': testPassengerId,
      });

      print('✅ get_or_create_conversation function exists');
      print('   Returned conversation ID type: ${conversationId?.runtimeType}');
      
    } catch (e) {
      print('❌ get_or_create_conversation function error: $e');
      print('   This function might be missing or have issues!');
    }
  }

  static Future<void> checkDatabaseSchema() async {
    print('\n🔍 Checking Database Schema...');
    print('=' * 30);

    try {
      // Check conversations table structure
      final conversations = await _client
          .from('conversations')
          .select('*')
          .limit(1);
      
      print('✅ Conversations table accessible');
      if (conversations.isNotEmpty) {
        final conv = conversations.first;
        print('   Sample conversation columns: ${conv.keys.toList()}');
        print('   Has booking_id: ${conv.containsKey('booking_id')}');
        print('   Has is_active: ${conv.containsKey('is_active')}');
        print('   Has unread_count_driver: ${conv.containsKey('unread_count_driver')}');
        print('   Has unread_count_passenger: ${conv.containsKey('unread_count_passenger')}');
      }

      // Check bookings table structure
      final bookings = await _client
          .from('bookings')
          .select('*')
          .limit(1);
      
      print('✅ Bookings table accessible');
      if (bookings.isNotEmpty) {
        final booking = bookings.first;
        print('   Sample booking columns: ${booking.keys.toList()}');
      }

    } catch (e) {
      print('❌ Database schema check error: $e');
    }
  }
}

// Run the test
void main() async {
  await DriverDashboardTest.testDriverBookingFlow();
  await DriverDashboardTest.checkDatabaseSchema();
}
