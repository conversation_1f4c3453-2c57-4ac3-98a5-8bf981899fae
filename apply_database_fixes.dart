import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Script to apply database fixes for the ride-sharing app
/// Run this script to fix foreign key issues and improve database schema
void main() async {
  print('🚀 Starting database fixes application...');
  
  try {
    // Initialize Supabase (you'll need to add your credentials)
    await Supabase.initialize(
      url: 'YOUR_SUPABASE_URL',
      anon<PERSON>ey: 'YOUR_SUPABASE_ANON_KEY',
    );
    
    final client = Supabase.instance.client;
    
    print('✅ Connected to Supabase');
    
    // Read the SQL fixes file
    final sqlFile = File('supabase_fixes.sql');
    if (!sqlFile.existsSync()) {
      print('❌ Error: supabase_fixes.sql file not found');
      exit(1);
    }
    
    final sqlContent = await sqlFile.readAsString();
    print('📄 Read SQL fixes file');
    
    // Split SQL content into individual statements
    final statements = sqlContent
        .split(';')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty && !s.startsWith('--'))
        .toList();
    
    print('🔧 Found ${statements.length} SQL statements to execute');
    
    // Execute each statement
    for (int i = 0; i < statements.length; i++) {
      final statement = statements[i];
      if (statement.isEmpty) continue;
      
      try {
        print('⚡ Executing statement ${i + 1}/${statements.length}...');
        await client.rpc('exec_sql', params: {'sql': statement});
        print('✅ Statement ${i + 1} executed successfully');
      } catch (e) {
        print('⚠️  Warning: Statement ${i + 1} failed: $e');
        // Continue with other statements
      }
    }
    
    print('🎉 Database fixes application completed!');
    
    // Test the fixes
    await testDatabaseFixes(client);
    
  } catch (e) {
    print('❌ Error applying database fixes: $e');
    exit(1);
  }
}

/// Test the database fixes to ensure they work correctly
Future<void> testDatabaseFixes(SupabaseClient client) async {
  print('\n🧪 Testing database fixes...');
  
  try {
    // Test 1: Check if foreign key constraints exist
    print('🔍 Test 1: Checking foreign key constraints...');
    
    final constraints = await client
        .from('information_schema.table_constraints')
        .select('constraint_name, table_name, constraint_type')
        .eq('constraint_type', 'FOREIGN KEY')
        .inFilter('table_name', ['conversations', 'bookings', 'messages']);
    
    print('✅ Found ${constraints.length} foreign key constraints');
    
    // Test 2: Test database functions
    print('🔍 Test 2: Testing database functions...');
    
    try {
      // Test get_driver_bookings function (with dummy data)
      await client.rpc('get_driver_bookings', params: {
        'p_driver_id': '00000000-0000-0000-0000-000000000000',
        'p_status': 'pending',
      });
      print('✅ get_driver_bookings function works');
    } catch (e) {
      print('⚠️  get_driver_bookings function test failed: $e');
    }
    
    try {
      // Test get_passenger_bookings function (with dummy data)
      await client.rpc('get_passenger_bookings', params: {
        'p_passenger_id': '00000000-0000-0000-0000-000000000000',
        'p_status': 'accepted',
      });
      print('✅ get_passenger_bookings function works');
    } catch (e) {
      print('⚠️  get_passenger_bookings function test failed: $e');
    }
    
    try {
      // Test get_or_create_conversation function (with dummy data)
      await client.rpc('get_or_create_conversation', params: {
        'p_trip_id': '00000000-0000-0000-0000-000000000000',
        'p_booking_id': null,
        'p_driver_id': '00000000-0000-0000-0000-000000000000',
        'p_passenger_id': '11111111-1111-1111-1111-111111111111',
      });
      print('✅ get_or_create_conversation function works');
    } catch (e) {
      print('⚠️  get_or_create_conversation function test failed: $e');
    }
    
    print('🎉 Database testing completed!');
    
  } catch (e) {
    print('❌ Error testing database fixes: $e');
  }
}

/// Alternative method: Apply fixes using direct SQL execution
/// Use this if the RPC method doesn't work
Future<void> applyFixesDirectly() async {
  print('🔧 Applying fixes using direct SQL execution...');
  
  final fixes = [
    // Fix 1: Add foreign key constraints for conversations
    '''
    ALTER TABLE public.conversations 
    DROP CONSTRAINT IF EXISTS conversations_driver_id_fkey;
    
    ALTER TABLE public.conversations 
    ADD CONSTRAINT conversations_driver_id_fkey 
    FOREIGN KEY (driver_id) REFERENCES public.users(id) ON DELETE CASCADE;
    ''',
    
    // Fix 2: Add foreign key constraints for bookings
    '''
    ALTER TABLE public.bookings 
    DROP CONSTRAINT IF EXISTS bookings_passenger_id_fkey;
    
    ALTER TABLE public.bookings 
    ADD CONSTRAINT bookings_passenger_id_fkey 
    FOREIGN KEY (passenger_id) REFERENCES public.users(id) ON DELETE CASCADE;
    ''',
    
    // Fix 3: Ensure trips have driver_id
    r'''
    DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                       WHERE table_name = 'trips' AND column_name = 'driver_id') THEN
            ALTER TABLE public.trips ADD COLUMN driver_id UUID;
        END IF;
    END $$;

    UPDATE public.trips
    SET driver_id = leader_id
    WHERE driver_id IS NULL;
    ''',
  ];
  
  final client = Supabase.instance.client;
  
  for (int i = 0; i < fixes.length; i++) {
    try {
      print('⚡ Applying fix ${i + 1}/${fixes.length}...');
      await client.rpc('exec_sql', params: {'sql': fixes[i]});
      print('✅ Fix ${i + 1} applied successfully');
    } catch (e) {
      print('❌ Fix ${i + 1} failed: $e');
    }
  }
}

/// Instructions for manual application
void printManualInstructions() {
  print('''
📋 MANUAL DATABASE FIXES INSTRUCTIONS:

If the automated script doesn't work, apply these fixes manually in your Supabase SQL editor:

1. 🔗 Fix Foreign Key Constraints:
   - Open Supabase Dashboard → SQL Editor
   - Copy and paste the content of 'supabase_fixes.sql'
   - Execute the SQL statements

2. 🔍 Verify the fixes:
   - Check that foreign key constraints exist
   - Test the database functions
   - Ensure no PostgrestException errors occur

3. 🚀 Restart your Flutter app:
   - The app should now work without foreign key errors
   - Bookings should appear correctly for drivers and passengers
   - Messages should work without "قيد التطوير" placeholder

4. 📱 Test the features:
   - Create a booking as a passenger
   - Accept it as a driver
   - Check that it appears in both interfaces
   - Test the messaging functionality

For support, check the console logs for detailed error messages.
''');
}
