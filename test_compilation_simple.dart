import 'package:flutter/material.dart';
import 'lib/services/notification_service.dart';
import 'lib/models/notification_model.dart';

void main() async {
  print('🧪 Testing compilation...');
  
  try {
    // Test NotificationService methods
    await NotificationService.initialize();
    print('✅ NotificationService.initialize() works');
    
    // Test getUserNotifications with limit
    final notifications = await NotificationService.getUserNotifications('test-user', limit: 10);
    print('✅ getUserNotifications with limit works: ${notifications.length} notifications');
    
    // Test subscribeToUserNotifications with callback
    final subscription = NotificationService.subscribeToUserNotifications('test-user', (notification) {
      print('Notification received: ${notification.title}');
    });
    print('✅ subscribeToUserNotifications with callback works');
    
    // Test missing methods
    await NotificationService.showLocalNotification(
      id: 1,
      title: 'Test',
      body: 'Test body',
    );
    print('✅ showLocalNotification works');
    
    await NotificationService.sendBookingRequestNotification(
      driverId: 'driver-id',
      passengerId: 'passenger-id',
      bookingId: 'booking-id',
      tripRoute: 'Test Route',
      passengerName: 'Test Passenger',
    );
    print('✅ sendBookingRequestNotification works');
    
    await NotificationService.sendBookingAcceptanceNotification(
      passengerId: 'passenger-id',
      driverId: 'driver-id',
      bookingId: 'booking-id',
      tripRoute: 'Test Route',
      driverName: 'Test Driver',
    );
    print('✅ sendBookingAcceptanceNotification works');
    
    await NotificationService.sendBookingRejectionNotification(
      passengerId: 'passenger-id',
      driverId: 'driver-id',
      bookingId: 'booking-id',
      tripRoute: 'Test Route',
      driverName: 'Test Driver',
      rejectionReason: 'Test reason',
    );
    print('✅ sendBookingRejectionNotification works');
    
    print('🎉 All compilation tests passed!');
    
  } catch (e) {
    print('❌ Compilation test failed: $e');
  }
}
