// Test script to validate the complete booking system
// Run this to ensure all components work together properly

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'lib/services/booking_service.dart';
import 'lib/services/notification_service.dart';
import 'lib/services/message_service.dart';
import 'lib/models/booking_model.dart';
import 'lib/models/trip_model.dart';
import 'lib/models/user_model.dart';

class BookingSystemTest {
  static final _client = Supabase.instance.client;

  static Future<void> runCompleteTest() async {
    print('🧪 Starting Complete Booking System Test');
    print('=' * 50);

    try {
      // Step 1: Create test data
      print('\n📋 Step 1: Creating test data...');
      final testData = await _createTestData();
      if (testData == null) {
        print('❌ Failed to create test data');
        return;
      }

      // Step 2: Test booking creation
      print('\n📋 Step 2: Testing booking creation...');
      final booking = await _testBookingCreation(testData);
      if (booking == null) {
        print('❌ Booking creation failed');
        return;
      }

      // Step 3: Test notification system
      print('\n📋 Step 3: Testing notification system...');
      await _testNotificationSystem(booking);

      // Step 4: Test booking acceptance
      print('\n📋 Step 4: Testing booking acceptance...');
      final acceptResult = await _testBookingAcceptance(booking);
      if (!acceptResult) {
        print('❌ Booking acceptance failed');
        return;
      }

      // Step 5: Test conversation creation
      print('\n📋 Step 5: Testing conversation creation...');
      await _testConversationCreation(booking);

      // Step 6: Test message system
      print('\n📋 Step 6: Testing message system...');
      await _testMessageSystem(booking);

      // Step 7: Test dashboard data
      print('\n📋 Step 7: Testing dashboard data...');
      await _testDashboardData(testData['driver']);

      print('\n✅ All tests completed successfully!');
      print('🎉 Booking system is working properly');

    } catch (e) {
      print('❌ Test failed with error: $e');
      print('Stack trace: ${StackTrace.current}');
    }
  }

  static Future<Map<String, dynamic>?> _createTestData() async {
    try {
      // Create test driver
      final driverData = {
        'id': 'test-driver-${DateTime.now().millisecondsSinceEpoch}',
        'email': '<EMAIL>',
        'full_name': 'سائق تجريبي',
        'phone': '+212600000001',
        'is_driver': true,
        'created_at': DateTime.now().toIso8601String(),
      };

      // Create test passenger
      final passengerData = {
        'id': 'test-passenger-${DateTime.now().millisecondsSinceEpoch}',
        'email': '<EMAIL>',
        'full_name': 'مسافر تجريبي',
        'phone': '+212600000002',
        'is_driver': false,
        'created_at': DateTime.now().toIso8601String(),
      };

      // Create test trip
      final tripData = {
        'id': 'test-trip-${DateTime.now().millisecondsSinceEpoch}',
        'driver_id': driverData['id'],
        'leader_id': driverData['id'],
        'from_city': 'الرباط',
        'to_city': 'الدار البيضاء',
        'departure_time': DateTime.now().add(const Duration(hours: 2)).toIso8601String(),
        'available_seats': 3,
        'price_per_seat': 50.0,
        'status': 'active',
        'created_at': DateTime.now().toIso8601String(),
      };

      print('✅ Test data created successfully');
      return {
        'driver': UserModel.fromJson(driverData),
        'passenger': UserModel.fromJson(passengerData),
        'trip': TripModel.fromJson(tripData),
      };
    } catch (e) {
      print('❌ Error creating test data: $e');
      return null;
    }
  }

  static Future<BookingModel?> _testBookingCreation(Map<String, dynamic> testData) async {
    try {
      final trip = testData['trip'] as TripModel;
      final passenger = testData['passenger'] as UserModel;
      final driver = testData['driver'] as UserModel;

      final result = await BookingService.createBooking(
        tripId: trip.id,
        passengerId: passenger.id,
        driverId: driver.id,
        seatsBooked: 1,
        totalPrice: 50.0,
        bookingType: 'manual',
        specialRequests: 'اختبار النظام',
      );
      
      if (result['success'] == true) {
        print('✅ Booking created successfully');
        
        // Fetch the created booking
        final bookings = await BookingService.getPendingBookings(driver.id);
        if (bookings.isNotEmpty) {
          return bookings.first;
        }
      }
      
      print('❌ Booking creation failed: ${result['error']}');
      return null;
    } catch (e) {
      print('❌ Error in booking creation test: $e');
      return null;
    }
  }

  static Future<void> _testNotificationSystem(BookingModel booking) async {
    try {
      await NotificationService.sendBookingRequestNotification(
        driverId: booking.driverId,
        passengerId: booking.passengerId,
        bookingId: booking.id,
        tripRoute: '${booking.trip?.fromCity} ← ${booking.trip?.toCity}',
        passengerName: booking.passenger?.fullName ?? 'مسافر تجريبي',
      );
      
      print('✅ Notification system test passed');
    } catch (e) {
      print('❌ Notification system test failed: $e');
    }
  }

  static Future<bool> _testBookingAcceptance(BookingModel booking) async {
    try {
      final result = await BookingService.acceptBooking(booking.id);
      
      if (result['success'] == true) {
        print('✅ Booking acceptance test passed');
        return true;
      } else {
        print('❌ Booking acceptance failed: ${result['error']}');
        return false;
      }
    } catch (e) {
      print('❌ Error in booking acceptance test: $e');
      return false;
    }
  }

  static Future<void> _testConversationCreation(BookingModel booking) async {
    try {
      final conversation = await MessageService.getOrCreateConversation(
        tripId: booking.tripId,
        bookingId: booking.id,
        driverId: booking.driverId,
        passengerId: booking.passengerId,
      );
      
      if (conversation != null) {
        print('✅ Conversation creation test passed');
      } else {
        print('❌ Conversation creation test failed');
      }
    } catch (e) {
      print('❌ Error in conversation creation test: $e');
    }
  }

  static Future<void> _testMessageSystem(BookingModel booking) async {
    try {
      final result = await MessageService.sendSystemMessage(
        tripId: booking.tripId,
        bookingId: booking.id,
        senderId: booking.driverId,
        receiverId: booking.passengerId,
        content: 'مرحباً! تم قبول حجزك بنجاح. هذه رسالة تجريبية.',
      );
      
      if (result['success'] == true) {
        print('✅ Message system test passed');
      } else {
        print('❌ Message system test failed: ${result['error']}');
      }
    } catch (e) {
      print('❌ Error in message system test: $e');
    }
  }

  static Future<void> _testDashboardData(UserModel driver) async {
    try {
      final pendingBookings = await BookingService.getPendingBookings(driver.id);
      final allBookings = await BookingService.getDriverBookings(driver.id);
      final conversations = await MessageService.getUserConversations(driver.id);
      
      print('✅ Dashboard data test passed');
      print('   - Pending bookings: ${pendingBookings.length}');
      print('   - Total bookings: ${allBookings.length}');
      print('   - Conversations: ${conversations.length}');
    } catch (e) {
      print('❌ Error in dashboard data test: $e');
    }
  }
}

// Run the test
void main() async {
  await BookingSystemTest.runCompleteTest();
}
