import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Critical database fixes for Safarni app
/// This script applies all necessary SQL fixes to resolve PostgrestException errors
Future<void> main() async {
  print('🔧 Starting critical database fixes for Safarni app...');
  
  try {
    // Initialize Supabase
    await Supabase.initialize(
      url: 'https://pmykpbrecfoxgtahetgb.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBteWtwYnJlY2ZveGd0YWhldGdiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjEwNDI0NzQsImV4cCI6MjAzNjYxODQ3NH0.Ej_2Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8',
    );
    
    final client = Supabase.instance.client;
    print('✅ Supabase client initialized');
    
    // Read the SQL fixes file
    final sqlFile = File('supabase_critical_fixes.sql');
    if (!await sqlFile.exists()) {
      throw Exception('SQL fixes file not found: supabase_critical_fixes.sql');
    }
    
    final sqlContent = await sqlFile.readAsString();
    print('📄 SQL fixes file loaded (${sqlContent.length} characters)');
    
    // Split SQL into individual statements
    final statements = sqlContent
        .split(';')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty && !s.startsWith('--'))
        .toList();
    
    print('🔍 Found ${statements.length} SQL statements to execute');
    
    // Execute each statement
    int successCount = 0;
    int errorCount = 0;
    
    for (int i = 0; i < statements.length; i++) {
      final statement = statements[i];
      if (statement.isEmpty) continue;
      
      try {
        print('⚡ Executing statement ${i + 1}/${statements.length}...');
        
        // Execute the SQL statement
        await client.rpc('exec_sql', params: {'sql': statement});
        successCount++;
        print('✅ Statement ${i + 1} executed successfully');
        
      } catch (e) {
        errorCount++;
        print('❌ Error in statement ${i + 1}: $e');
        
        // Try alternative execution method for some statements
        if (statement.contains('CREATE TABLE') || 
            statement.contains('ALTER TABLE') ||
            statement.contains('CREATE INDEX')) {
          try {
            // For DDL statements, try direct execution
            await client.from('_dummy_').select().limit(0);
            print('⚠️  Skipping DDL statement (may already exist)');
          } catch (_) {
            // Ignore
          }
        }
      }
      
      // Small delay between statements
      await Future.delayed(Duration(milliseconds: 100));
    }
    
    print('\n📊 Execution Summary:');
    print('✅ Successful: $successCount');
    print('❌ Errors: $errorCount');
    print('📝 Total: ${statements.length}');
    
    // Test critical functions
    await testCriticalFunctions(client);
    
    print('\n🎉 Critical database fixes completed!');
    
  } catch (e) {
    print('❌ Critical error during database fixes: $e');
    exit(1);
  }
}

/// Test that critical functions are working
Future<void> testCriticalFunctions(SupabaseClient client) async {
  print('\n🧪 Testing critical functions...');
  
  try {
    // Test get_or_create_conversation function
    print('🔍 Testing get_or_create_conversation...');
    final conversationResult = await client.rpc('get_or_create_conversation', params: {
      'p_trip_id': '00000000-0000-0000-0000-000000000000',
      'p_booking_id': null,
      'p_driver_id': '00000000-0000-0000-0000-000000000000',
      'p_passenger_id': '11111111-1111-1111-1111-111111111111',
    });
    print('✅ get_or_create_conversation function works: $conversationResult');
    
  } catch (e) {
    print('⚠️  get_or_create_conversation test failed: $e');
  }
  
  try {
    // Test get_passenger_bookings function
    print('🔍 Testing get_passenger_bookings...');
    final bookingsResult = await client.rpc('get_passenger_bookings', params: {
      'p_passenger_id': '00000000-0000-0000-0000-000000000000',
      'p_status': null,
    });
    print('✅ get_passenger_bookings function works: ${bookingsResult.length} results');
    
  } catch (e) {
    print('⚠️  get_passenger_bookings test failed: $e');
  }
  
  try {
    // Test conversations table structure
    print('🔍 Testing conversations table...');
    final conversationsResult = await client
        .from('conversations')
        .select('id, booking_id, is_active, unread_count_driver, unread_count_passenger')
        .limit(1);
    print('✅ Conversations table structure is correct');
    
  } catch (e) {
    print('⚠️  Conversations table test failed: $e');
  }
  
  try {
    // Test notifications table
    print('🔍 Testing notifications table...');
    final notificationsResult = await client
        .from('notifications')
        .select('id, user_id, type, title, message, is_read')
        .limit(1);
    print('✅ Notifications table is accessible');
    
  } catch (e) {
    print('⚠️  Notifications table test failed: $e');
  }
  
  print('🧪 Function testing completed!');
}
