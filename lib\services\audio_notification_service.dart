import 'package:flutter/foundation.dart';
// import 'package:audioplayers/audioplayers.dart';  // Commented out for web compatibility
import 'package:flutter/services.dart';

/// Service for handling audio notifications and sound effects
class AudioNotificationService {
  static final AudioNotificationService _instance = AudioNotificationService._internal();
  factory AudioNotificationService() => _instance;
  AudioNotificationService._internal();

  // Audio player disabled for web compatibility
  bool _isInitialized = false;

  /// Initialize the audio service (disabled for web)
  Future<bool> initialize() async {
    try {
      if (kDebugMode) {
        print('Audio service disabled for web compatibility');
      }
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Audio Notification Service initialized');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Audio service initialization error: $e');
      }
      return false;
    }
  }

  /// Play search success sound
  Future<void> playSearchSuccess() async {
    if (!_isInitialized) return;
    
    try {
      // Try to play local MP3 first, fallback to system sound
      await _playLocalSound('assets/audio/search_done.mp3');
    } catch (e) {
      // Fallback to system haptic feedback
      await _playSystemFeedback();
    }
  }

  /// Play search error sound
  Future<void> playSearchError() async {
    if (!_isInitialized) return;
    
    try {
      await _playLocalSound('assets/audio/search_error.mp3');
    } catch (e) {
      // Fallback to system haptic feedback
      await _playSystemErrorFeedback();
    }
  }

  /// Play listening start sound
  Future<void> playListeningStart() async {
    if (!_isInitialized) return;
    
    try {
      await _playLocalSound('assets/audio/listening_start.mp3');
    } catch (e) {
      // Fallback to gentle haptic
      await HapticFeedback.selectionClick();
    }
  }

  /// Play listening stop sound
  Future<void> playListeningStop() async {
    if (!_isInitialized) return;
    
    try {
      await _playLocalSound('assets/audio/listening_stop.mp3');
    } catch (e) {
      // Fallback to gentle haptic
      await HapticFeedback.selectionClick();
    }
  }

  /// Play local audio file (disabled for web)
  Future<void> _playLocalSound(String assetPath) async {
    try {
      if (kDebugMode) {
        print('🔊 Audio playback disabled for web compatibility: $assetPath');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to play sound $assetPath: $e');
      }
      rethrow;
    }
  }

  /// Fallback system feedback for success
  Future<void> _playSystemFeedback() async {
    try {
      await HapticFeedback.lightImpact();
      // Add a small delay for better UX
      await Future.delayed(const Duration(milliseconds: 100));
      await HapticFeedback.selectionClick();
    } catch (e) {
      if (kDebugMode) {
        print('❌ System feedback error: $e');
      }
    }
  }

  /// Fallback system feedback for errors
  Future<void> _playSystemErrorFeedback() async {
    try {
      await HapticFeedback.heavyImpact();
    } catch (e) {
      if (kDebugMode) {
        print('❌ System error feedback error: $e');
      }
    }
  }

  /// Stop all audio playback (disabled for web)
  Future<void> stopAll() async {
    try {
      if (kDebugMode) {
        print('Audio stop disabled for web compatibility');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Stop audio error: $e');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    // Audio player disposal disabled for web compatibility
  }
}
