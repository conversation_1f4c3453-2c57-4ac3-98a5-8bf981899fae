import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_theme.dart';
import '../../models/booking_model.dart';
import '../../services/booking_service.dart';

class BookingRequestCard extends StatefulWidget {
  final BookingModel booking;
  final VoidCallback? onBookingUpdated;

  const BookingRequestCard({
    super.key,
    required this.booking,
    this.onBookingUpdated,
  });

  @override
  State<BookingRequestCard> createState() => _BookingRequestCardState();
}

class _BookingRequestCardState extends State<BookingRequestCard> {
  bool _isProcessing = false;

  // Helper method to safely decode passenger details
  Map<String, dynamic> _getPassengerDetails() {
    final details = widget.booking.passengerDetails;
    if (details == null) return {};

    try {
      // Safe parsing: handle both String (JSON) and Map<String, dynamic>
      final dynamic rawDetails = details;
      final Map<String, dynamic> passengerDetails = rawDetails is String
          ? jsonDecode(rawDetails) as Map<String, dynamic>
          : (rawDetails as Map<String, dynamic>);

      return passengerDetails;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error decoding passenger details: $e');
      }
      return {};
    }
  }

  // Helper method to safely get passenger name with multiple fallbacks
  String _getPassengerName() {
    final passenger = widget.booking.passenger;
    final passengerDetails = _getPassengerDetails();

    // Try passenger object first
    if (passenger?.fullName != null && passenger!.fullName!.isNotEmpty) {
      return passenger.fullName!;
    }

    // Try details with multiple key variations
    final nameValue = passengerDetails['name'] ??
                     passengerDetails['full_name'] ??
                     passengerDetails['fullName'];

    if (nameValue != null && nameValue.toString().isNotEmpty) {
      return nameValue.toString();
    }

    return 'مسافر';
  }

  // Helper method to safely get passenger phone
  String _getPassengerPhone() {
    final passengerDetails = _getPassengerDetails();

    return (passengerDetails['phone'] ??
            passengerDetails['phone_number'] ??
            passengerDetails['phoneNumber'] ??
            '').toString();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Passenger Info
            Row(
              children: [
                _buildPassengerAvatar(),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPassengerInfo(),
                ),
                _buildBookingBadge(),
              ],
            ),

            const SizedBox(height: 16),

            // Trip Info
            _buildTripInfo(),

            if (widget.booking.message != null) ...[
              const SizedBox(height: 12),
              _buildMessage(),
            ],

            if (widget.booking.specialRequests != null) ...[
              const SizedBox(height: 12),
              _buildSpecialRequests(),
            ],

            const SizedBox(height: 16),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildPassengerAvatar() {
    final passenger = widget.booking.passenger;
    final passengerDetails = _getPassengerDetails();

    String? imageUrl;
    if (passenger?.profileImageUrl != null) {
      imageUrl = passenger!.profileImageUrl;
    } else {
      // Safe lookup with multiple key variations
      final imageValue = passengerDetails['profile_image_url'] ??
                        passengerDetails['profileImageUrl'] ??
                        passengerDetails['avatar'] ??
                        passengerDetails['image_url'];

      if (imageValue != null && imageValue.toString().isNotEmpty) {
        imageUrl = imageValue.toString();
      } else {
        imageUrl = null;
      }
    }

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipOval(
        child: imageUrl != null
            ? CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.surface,
                  child: const Center(
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.surface,
                  child: const Icon(
                    Icons.person,
                    color: AppColors.textSecondary,
                    size: 30,
                  ),
                ),
              )
            : Container(
                color: AppColors.surface,
                child: const Icon(
                  Icons.person,
                  color: AppColors.textSecondary,
                  size: 30,
                ),
              ),
      ),
    );
  }

  Widget _buildPassengerInfo() {
    final passenger = widget.booking.passenger;

    // Use safe helper methods
    final String passengerName = _getPassengerName();
    final String passengerPhone = _getPassengerPhone();

    // Get rating info
    double rating = passenger?.rating ?? 0.0;
    int totalRatings = passenger?.totalRatings ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          passengerName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(
              Icons.star,
              color: AppColors.warning,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              rating.toStringAsFixed(1),
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '($totalRatings تقييم)',
              style: const TextStyle(
                color: AppColors.textTertiary,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        if (passengerPhone.isNotEmpty) ...[
          Text(
            'الهاتف: $passengerPhone',
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
        ],
        Text(
          'انضم في ${_formatDate(passenger?.createdAt)}',
          style: const TextStyle(
            color: AppColors.textTertiary,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildBookingBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.booking.bookingType == 'instant'
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.booking.bookingType == 'instant'
              ? AppColors.success.withValues(alpha: 0.3)
              : AppColors.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        widget.booking.bookingType == 'instant' ? 'فوري' : 'يدوي',
        style: TextStyle(
          color: widget.booking.bookingType == 'instant'
              ? AppColors.success
              : AppColors.warning,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTripInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.airline_seat_recline_normal,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.booking.seatsBooked} مقعد',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${widget.booking.totalPrice.toInt()} درهم',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.secondary,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          if (widget.booking.trip != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.route,
                  color: AppColors.textSecondary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${widget.booking.trip!.fromCity} ← ${widget.booking.trip!.toCity}',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.message,
                color: AppColors.info,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'رسالة من المسافر',
                style: TextStyle(
                  color: AppColors.info,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.booking.message!,
            style: TextStyle(
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialRequests() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.priority_high,
                color: AppColors.warning,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'طلبات خاصة',
                style: TextStyle(
                  color: AppColors.warning,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.booking.specialRequests!,
            style: TextStyle(
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _isProcessing ? null : () => _rejectBooking(),
            icon: const Icon(Icons.close, size: 18),
            label: const Text('رفض'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.error,
              side: BorderSide(color: AppColors.error),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: _isProcessing ? null : () => _acceptBooking(),
            icon: _isProcessing
                ? const SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.check, size: 18),
            label: Text(_isProcessing ? 'جاري المعالجة...' : 'قبول'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _acceptBooking() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final result = await BookingService.acceptBooking(widget.booking.id);

      if (mounted) {
        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'تم قبول الحجز بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
          widget.onBookingUpdated?.call();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? 'حدث خطأ أثناء قبول الحجز'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<void> _rejectBooking() async {
    // Show rejection reason dialog
    final reason = await _showRejectionDialog();
    if (reason == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final result = await BookingService.rejectBooking(
        widget.booking.id,
        rejectionReason: reason.isEmpty ? null : reason,
      );

      if (mounted) {
        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'تم رفض الحجز'),
              backgroundColor: AppColors.warning,
            ),
          );
          widget.onBookingUpdated?.call();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? 'حدث خطأ أثناء رفض الحجز'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<String?> _showRejectionDialog() async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سبب الرفض'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يمكنك إضافة سبب الرفض (اختياري):'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'مثل: الرحلة ممتلئة، تغيير في الموعد...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('رفض الحجز'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'غير محدد';

    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 30) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 365) {
      return 'منذ ${(difference.inDays / 30).floor()} شهر';
    } else {
      return 'منذ ${(difference.inDays / 365).floor()} سنة';
    }
  }
}
