import 'user_model.dart';

class MessageModel {
  final String id;
  final String tripId;
  final String? bookingId;
  final String senderId;
  final String receiverId;
  final UserModel? sender;
  final UserModel? receiver;
  final String content;
  final String messageType; // 'text', 'audio', 'location', 'image', 'system'
  final Map<String, dynamic>? locationData;
  final bool isRead;
  final bool isDelivered;
  final DateTime createdAt;
  final DateTime updatedAt;

  MessageModel({
    required this.id,
    required this.tripId,
    this.bookingId,
    required this.senderId,
    required this.receiverId,
    this.sender,
    this.receiver,
    required this.content,
    this.messageType = 'text',
    this.locationData,
    this.isRead = false,
    this.isDelivered = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'] as String,
      tripId: json['trip_id'] as String,
      bookingId: json['booking_id'] as String?,
      senderId: json['sender_id'] as String,
      receiverId: json['receiver_id'] as String,
      sender: json['sender'] != null
          ? UserModel.fromJson(json['sender'] as Map<String, dynamic>)
          : null,
      receiver: json['receiver'] != null
          ? UserModel.fromJson(json['receiver'] as Map<String, dynamic>)
          : null,
      content: json['content'] as String,
      messageType: json['message_type'] as String? ?? 'text',
      locationData: json['location_data'] as Map<String, dynamic>?,
      isRead: json['is_read'] as bool? ?? false,
      isDelivered: json['is_delivered'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'booking_id': bookingId,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'content': content,
      'message_type': messageType,
      'location_data': locationData,
      'is_read': isRead,
      'is_delivered': isDelivered,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  MessageModel copyWith({
    String? id,
    String? tripId,
    String? bookingId,
    String? senderId,
    String? receiverId,
    UserModel? sender,
    UserModel? receiver,
    String? content,
    String? messageType,
    Map<String, dynamic>? locationData,
    bool? isRead,
    bool? isDelivered,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MessageModel(
      id: id ?? this.id,
      tripId: tripId ?? this.tripId,
      bookingId: bookingId ?? this.bookingId,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      sender: sender ?? this.sender,
      receiver: receiver ?? this.receiver,
      content: content ?? this.content,
      messageType: messageType ?? this.messageType,
      locationData: locationData ?? this.locationData,
      isRead: isRead ?? this.isRead,
      isDelivered: isDelivered ?? this.isDelivered,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get isLocationMessage => messageType == 'location';
  bool get isImageMessage => messageType == 'image';
  bool get isAudioMessage => messageType == 'audio';
  bool get isSystemMessage => messageType == 'system';
  bool get isTextMessage => messageType == 'text';

  // Get formatted time
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  // Create system message
  static MessageModel createSystemMessage({
    required String tripId,
    String? bookingId,
    required String senderId,
    required String receiverId,
    required String content,
  }) {
    final now = DateTime.now();
    return MessageModel(
      id: '', // Will be generated by Supabase
      tripId: tripId,
      bookingId: bookingId,
      senderId: senderId,
      receiverId: receiverId,
      content: content,
      messageType: 'system',
      isRead: false,
      isDelivered: true,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Create location message
  static MessageModel createLocationMessage({
    required String tripId,
    String? bookingId,
    required String senderId,
    required String receiverId,
    required double latitude,
    required double longitude,
    String? address,
  }) {
    final now = DateTime.now();
    return MessageModel(
      id: '', // Will be generated by Supabase
      tripId: tripId,
      bookingId: bookingId,
      senderId: senderId,
      receiverId: receiverId,
      content: address ?? 'موقع مشترك',
      messageType: 'location',
      locationData: {
        'latitude': latitude,
        'longitude': longitude,
        'address': address,
      },
      isRead: false,
      isDelivered: false,
      createdAt: now,
      updatedAt: now,
    );
  }
}
