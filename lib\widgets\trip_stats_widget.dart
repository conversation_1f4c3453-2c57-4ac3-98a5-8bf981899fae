import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/rating_service.dart';

class TripStatsWidget extends StatefulWidget {
  final String userId;
  final bool showDetailed;

  const TripStatsWidget({
    super.key,
    required this.userId,
    this.showDetailed = true,
  });

  @override
  State<TripStatsWidget> createState() => _TripStatsWidgetState();
}

class _TripStatsWidgetState extends State<TripStatsWidget> {
  Map<String, dynamic> _stats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final stats = await RatingService.getUserTripStats(widget.userId);
      if (mounted) {
        setState(() {
          _stats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (widget.showDetailed) {
      return _buildDetailedStats();
    } else {
      return _buildCompactStats();
    }
  }

  Widget _buildDetailedStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'إحصائيات الرحلات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Rating overview
            _buildRatingOverview(),
            const SizedBox(height: 20),

            // Trip statistics
            _buildTripStatistics(),
            const SizedBox(height: 20),

            // Achievements
            _buildAchievements(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactStats() {
    final totalTrips = _stats['totalTrips'] ?? 0;
    final averageRating = _stats['averageRating'] ?? 0.0;
    final totalRatings = _stats['totalRatings'] ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.secondary.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.directions_car,
              label: 'الرحلات',
              value: totalTrips.toString(),
              color: AppColors.primary,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border,
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.star,
              label: 'التقييم',
              value: averageRating > 0 ? averageRating.toStringAsFixed(1) : '-',
              color: AppColors.warning,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border,
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.reviews,
              label: 'المراجعات',
              value: totalRatings.toString(),
              color: AppColors.secondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingOverview() {
    final averageRating = _stats['averageRating'] ?? 0.0;
    final totalRatings = _stats['totalRatings'] ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.star,
            color: AppColors.warning,
            size: 32,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  averageRating > 0 ? averageRating.toStringAsFixed(1) : 'لا يوجد تقييم',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '$totalRatings تقييم',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          if (averageRating > 0)
            Row(
              children: List.generate(5, (index) {
                return Icon(
                  index < averageRating.round() ? Icons.star : Icons.star_border,
                  color: AppColors.warning,
                  size: 20,
                );
              }),
            ),
        ],
      ),
    );
  }

  Widget _buildTripStatistics() {
    final driverTrips = _stats['driverTrips'] ?? 0;
    final passengerTrips = _stats['passengerTrips'] ?? 0;
    final completedTrips = _stats['completedTrips'] ?? 0;
    final totalTrips = _stats['totalTrips'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات الرحلات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                icon: Icons.drive_eta,
                label: 'كسائق',
                value: driverTrips.toString(),
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                icon: Icons.airline_seat_recline_normal,
                label: 'كمسافر',
                value: passengerTrips.toString(),
                color: AppColors.secondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                icon: Icons.check_circle,
                label: 'مكتملة',
                value: completedTrips.toString(),
                color: AppColors.success,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                icon: Icons.analytics,
                label: 'المجموع',
                value: totalTrips.toString(),
                color: AppColors.info,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAchievements() {
    final completedTrips = _stats['completedTrips'] ?? 0;
    final averageRating = _stats['averageRating'] ?? 0.0;
    final driverTrips = _stats['driverTrips'] ?? 0;

    final achievements = <Map<String, dynamic>>[];

    // Trip milestones
    if (completedTrips >= 100) {
      achievements.add({
        'icon': Icons.emoji_events,
        'title': 'مسافر محترف',
        'description': '100+ رحلة مكتملة',
        'color': AppColors.warning,
      });
    } else if (completedTrips >= 50) {
      achievements.add({
        'icon': Icons.star,
        'title': 'مسافر متمرس',
        'description': '50+ رحلة مكتملة',
        'color': AppColors.secondary,
      });
    } else if (completedTrips >= 10) {
      achievements.add({
        'icon': Icons.thumb_up,
        'title': 'مسافر نشط',
        'description': '10+ رحلة مكتملة',
        'color': AppColors.success,
      });
    }

    // Rating achievements
    if (averageRating >= 4.8) {
      achievements.add({
        'icon': Icons.diamond,
        'title': 'تقييم ممتاز',
        'description': 'تقييم ${averageRating.toStringAsFixed(1)}/5.0',
        'color': AppColors.warning,
      });
    } else if (averageRating >= 4.5) {
      achievements.add({
        'icon': Icons.star_rate,
        'title': 'تقييم عالي',
        'description': 'تقييم ${averageRating.toStringAsFixed(1)}/5.0',
        'color': AppColors.info,
      });
    }

    // Driver achievements
    if (driverTrips >= 20) {
      achievements.add({
        'icon': Icons.local_taxi,
        'title': 'سائق محترف',
        'description': '$driverTrips رحلة كسائق',
        'color': AppColors.primary,
      });
    }

    if (achievements.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإنجازات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: achievements.map((achievement) => _buildAchievementBadge(
            icon: achievement['icon'],
            title: achievement['title'],
            description: achievement['description'],
            color: achievement['color'],
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementBadge({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                description,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
